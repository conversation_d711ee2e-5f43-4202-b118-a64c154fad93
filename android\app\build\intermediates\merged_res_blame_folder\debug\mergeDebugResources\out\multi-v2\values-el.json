{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6892,7466,7573,7698", "endColumns": "109,106,124,109", "endOffsets": "6997,7568,7693,7803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,291,361,445,517,585,663,744,828,920,992,1074,1161,1245,1330,1413,1493,1564,1634,1722,1794,1874,1948", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "124,210,286,356,440,512,580,658,739,823,915,987,1069,1156,1240,1325,1408,1488,1559,1629,1717,1789,1869,1943,2025"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3056,4440,7808,7966,8036,8358,9369,9437,9515,12630,12714,12969,13041,13284,13371,13455,13615,13698,13856,14002,14072,14261,14482,14685,14759", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "3125,4521,7879,8031,8115,8425,9432,9510,9591,12709,12801,13036,13118,13366,13450,13535,13693,13773,13922,14067,14155,14328,14557,14754,14836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3215,3313,3416,3516,3619,3727,3833,14160", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3308,3411,3511,3614,3722,3828,3945,14256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1023,1105,1180,1255,1333,1408,1487,1557", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1018,1100,1175,1250,1328,1403,1482,1552,1675"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4255,4354,7002,7103,7208,8120,8201,12360,12449,12806,12887,13209,13540,13778,13927,14333,14412,14562", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "4349,4435,7098,7203,7295,8196,8290,12444,12534,12882,12964,13279,13610,13851,13997,14407,14477,14680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,310,412,529,615,681,781,863,926,1017,1082,1144,1213,1275,1329,1467,1524,1585,1639,1712,1865,1950,2034,2143,2224,2309,2399,2466,2532,2610,2695,2780,2852,2932,3012,3083,3175,3247,3344,3441,3515,3589,3691,3747,3819,3907,3999,4061,4125,4188,4304,4412,4521,4629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "220,305,407,524,610,676,776,858,921,1012,1077,1139,1208,1270,1324,1462,1519,1580,1634,1707,1860,1945,2029,2138,2219,2304,2394,2461,2527,2605,2690,2775,2847,2927,3007,3078,3170,3242,3339,3436,3510,3584,3686,3742,3814,3902,3994,4056,4120,4183,4299,4407,4516,4624,4715"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3130,3950,4052,4169,7300,7366,7884,8295,8430,8521,8586,8648,8717,8779,8833,8971,9028,9089,9143,9216,9596,9681,9765,9874,9955,10040,10130,10197,10263,10341,10426,10511,10583,10663,10743,10814,10906,10978,11075,11172,11246,11320,11422,11478,11550,11638,11730,11792,11856,11919,12035,12143,12252,12539", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "270,3210,4047,4164,4250,7361,7461,7961,8353,8516,8581,8643,8712,8774,8828,8966,9023,9084,9138,9211,9364,9676,9760,9869,9950,10035,10125,10192,10258,10336,10421,10506,10578,10658,10738,10809,10901,10973,11070,11167,11241,11315,11417,11473,11545,11633,11725,11787,11851,11914,12030,12138,12247,12355,12625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14841,14939", "endColumns": "97,101", "endOffsets": "14934,15036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,393,504,621,706,812,935,1024,1109,1200,1293,1388,1482,1582,1675,1770,1867,1958,2049,2134,2245,2354,2456,2567,2677,2785,2956,13123", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "388,499,616,701,807,930,1019,1104,1195,1288,1383,1477,1577,1670,1765,1862,1953,2044,2129,2240,2349,2451,2562,2672,2780,2951,3051,13204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4526,4637,4823,4955,5066,5247,5372,5490,5759,5948,6056,6226,6355,6533,6678,6747,6809", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "4632,4818,4950,5061,5242,5367,5485,5590,5943,6051,6221,6350,6528,6673,6742,6804,6887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5595", "endColumns": "163", "endOffsets": "5754"}}]}]}