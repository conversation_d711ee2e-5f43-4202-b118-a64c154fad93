{"name": "cc-rn-banking", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "^52.0.39", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.5", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.5", "expo-linking": "~7.0.5", "expo-notifications": "~0.29.14", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.7", "expo-web-browser": "~14.0.2", "i18next": "^24.2.2", "moment": "^2.30.1", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.4.0", "react-native": "0.76.6", "react-native-btr": "^2.2.1", "react-native-calendars": "^1.1308.1", "react-native-chart-kit": "^6.12.0", "react-native-dashed-line": "^1.1.0", "react-native-gesture-handler": "~2.20.2", "react-native-intl-phone-input": "^1.2.27", "react-native-maps": "1.18.0", "react-native-otp-entry": "^1.8.2", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.8.0", "react-native-swipe-list-view": "^3.2.9", "react-native-tab-view": "^4.0.6", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}