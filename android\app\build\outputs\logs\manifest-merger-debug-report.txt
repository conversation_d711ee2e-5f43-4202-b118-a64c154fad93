-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:1-36:12
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-49:12
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-57:12
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64916dec3207c110b65653508cdd652c\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fbd21e0f03e612ad3ec9e9d852e13a9\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9c27fde72cde99c3c3febe47f53e6f5\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ab42d11055b0e5e13e74fbbe79b2a40\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f682cebb3b8ac7b44e608da7b3d68f64\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03b0297fc2e61ba6168b032d13ef425b\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1237673d01c0cd5458eea709a733bd\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06fc7c047fc86a5350f9661b4c75ce0\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:2:1-31:12
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df4047845cba141e3d72a296b8ebd827\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9767d48e60644bdd474782a5024d9ccd\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba63eea34968ac021cedbd1b85bd502\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aa52d47348985c73b8cf35e1e65e62\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6cd8ec49b429079797bd87a9cbd6ff3\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe3ee9224a8196eee6ee54c26b36d74\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ed4efd95d8440ba0b6c7542219c081\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1dfd1b5a83d588f5e603577b9178d0\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87fa0d78838279af547bb791a0359734\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99cc69e026378dbd6f90620a53792b58\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31aae105eb04a406e2ec638f81d2b6bb\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41326908fbc54526a8c2668ccc81005d\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2927c22491b7bae5c3e8b02cbb6dcaf\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58ecd19d0aa0f88f0cfc56e03515ed8\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004bf2b17555a1d78bc2e33d5e6648a9\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0245299d52204dfeed11ab2f0057dc07\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635f7e61c4c8c520293b2abbd7328749\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dfea2d02a2f5252c44a957dbdd44a64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e77cf24b40b71eb50f74e133a7bc34f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\123cf6522c99c23fb07dc81c0a39f035\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7e8589b595a47af3e9ced4ba3efcd18\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddd1982f0bff2916f6b431d03c71d81\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b5082326aaded3911b6e6ec44be48d\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b105516173dd17fd78ec8185e8cc5101\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f02e74a5d81ac387218e6b268b524af\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30007fc325d59e5b0197043eba024cce\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8038d679c6201ccc8bf88cea2ee85a9a\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c962283642e48ac832ef3cf069ec7d35\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dd958c62274c71174c6ad8e15200343\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f899b0c191eabd106cb0793f29ac337\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2bcd653822bd9b07c43a787f3de1c7\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe005d8bf59ba00e237aa64d6c04b6d\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4233f5702469b5ea3a7f7cc111e192\transformed\animated-gif-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b62bd6770a1a0bd7e3a3f25f030bff9\transformed\webpsupport-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0cd3405513bf86b8aedb79eae3d7fc5\transformed\fresco-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99007c466855608ffe353adb75f2d342\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec2c412bae5546cfe5bcb2197c2a59d\transformed\animated-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f538b73d39080ea81e8ea047f79eeb8d\transformed\animated-drawable-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\774f298d909cfcbae87a53f1f0621476\transformed\vito-options-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795db646d5257b1dffa43100532574a6\transformed\drawee-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257371aebd960c24715bb6abbff9f5bf\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e602597bb3acfa2a82782b1a6d7284c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2101fa1a881bd2edef289cc4182707\transformed\memory-type-java-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec57f1f6f00a791e95b686e6b5e74c40\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\586e0e8333d5b42f64060d9d50a926dd\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a657d9f65b35d86386fed4edbd5dc299\transformed\imagepipeline-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\494d8f7885d899fb7b9b4393243be850\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caa2dff8b12681a2413f8bcfb00b93cd\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecbdd3f36fbda2007ff0555db1e2d48a\transformed\middleware-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ae22da40e9e11c25c5dbb3c54bcbdc\transformed\ui-common-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db4f5d8937d7c4955d8faea4dddc9086\transformed\soloader-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7524487013c78eb2b8ee578ae0584de7\transformed\fbcore-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e1fc5fcaa3ed5f5449494b4af1d023\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f701b1c825c6e4f40e596908bba825e7\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78058efdb2a613945fc64792040e9310\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8dcdc5c56667393d0b3e956be36660f\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\078f9f02e495f0dd0d4e161296c58ceb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37b335fa6456d069fae66478cd3f5d86\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bdef6d4c70169befe3abf2d5b3b6da\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee85571c65f69f6561466a11705304cf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8921afe01ea231b105108b56412c9411\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edaac9dd15858d6bddb889f7ae2dce70\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943c5e84007847618e929643a3f16713\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5857038c93ce878d6caa714f85550df\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a781e690c6f85408986c8e0bc88c5dbe\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed26e31f373a353d6df51f524243fa77\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e397bb62bef4ae7fa0227e458b86cc6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f773416775a47656b455505e7c2d836\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14df64deaa65d652801af737f01a39b9\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e111718ef3632e36e79757ab187886b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b51e513eaac6ddc479b7ab44cbe758\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aff1c1c001931892e6322b21062b5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2b839604b972af639853fc4fcf6ad4e\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ada69cf34ac3768f494c8eaa4a4a91a\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163bb7dfa58f2e00c622862ee5c8da95\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44ebbf2f2c8625d954228a1184185d\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37213c2832ac8fb03f8a9407c7109e91\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca31741dd4b7ed918dbb9b294b453c3\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ffb77c3facd0be7cf360be32700013e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\880c43895efa6facf988b03ab593d472\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e51d0b3859d8154ebe72962e057ff1\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5994bb38ec15e11bdc857c2750fe05\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a7f386a268f8e06f85d388d47b823ab\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\390a03af89b11bd85608373396a7a672\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39ab0d272159339cff56d09d2456bfd2\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b22009fa58ae9cfbdc13cf4633cdd1c8\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c77ba46fd4f7461df3ea066903c0cf73\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f9f12d82a908e44f849c25bba51e2dc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48b133c067113574b2e7afe09bf975f1\transformed\vito-renderer-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b927f0a827a362ae4d5f43d0882c12\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fa3ed7ff7791d06670ff1e405faada\transformed\hermes-android-0.76.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4baf8bc0e22c795bbafca92de503ec81\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b55bba363d9b043d59112d94c0d6f40\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da5319cab98f7711fb59418bc962b00\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348d8678be95df500a7b14340d72a3ca\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2cefc9402a998496405605999dac44\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70082ae80e0904f10db117483583134e\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f75cd99f07d3a6916f227f2ef992e672\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f2bc2c05b197d6b7caa82ee3b93570\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3469e458ac0f1dd7c5828e607eb0b375\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4cc817034b22846a69e32a2a6128b4ba\transformed\viewbinding-7.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a82e22a10173ac37fccabd03cddd5215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7906fb949d3e47a55c2c9ef793df54ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a0a71688d7c5ff7090d2346373f2ef\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d49441a16557301b02d5c0c5a8c62234\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\497e16e603db7b5b76461a52d25a0dda\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea564785c7682172e5c76fbb4c29e3c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20a2db9ec5e7f48674b6f3c79a8b161\transformed\fbjni-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43b68f82e3f97f4754b1a9793089d94\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4207dae3cc02f88b436ccf4c397fbb6\transformed\rxandroid-2.0.2\AndroidManifest.xml:15:1-21:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7e84112132efeaf5c10fc1fdd5671\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:3-62
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:3-64
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:3-68
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:3-63
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:20-76
queries
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:9:3-15:13
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:10:5-16:15
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:10:5-16:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
data
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
application
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-35:17
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-35:17
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-35:17
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9c27fde72cde99c3c3febe47f53e6f5\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9c27fde72cde99c3c3febe47f53e6f5\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03b0297fc2e61ba6168b032d13ef425b\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03b0297fc2e61ba6168b032d13ef425b\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:18:5-29:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:18:5-29:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df4047845cba141e3d72a296b8ebd827\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df4047845cba141e3d72a296b8ebd827\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e77cf24b40b71eb50f74e133a7bc34f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e77cf24b40b71eb50f74e133a7bc34f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a7f386a268f8e06f85d388d47b823ab\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a7f386a268f8e06f85d388d47b823ab\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\390a03af89b11bd85608373396a7a672\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\390a03af89b11bd85608373396a7a672\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7906fb949d3e47a55c2c9ef793df54ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7906fb949d3e47a55c2c9ef793df54ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7e84112132efeaf5c10fc1fdd5671\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7e84112132efeaf5c10fc1fdd5671\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
	android:label
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
	android:theme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
meta-data#com.google.android.geo.API_KEY
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:5-114
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:62-112
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:16-61
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:5-83
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:60-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:5-105
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:81-103
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:5-99
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:80-97
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:16-79
activity#com.camelcase.rnstarbank.MainActivity
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:5-33:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:167-209
	android:exported
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:44-134
	android:theme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:210-255
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:7-25:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:9-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:9-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.camelcase.rnstarbank+data:scheme:myapp
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:26:7-32:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:9-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:19-65
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:5-83
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:57-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:19-56
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64916dec3207c110b65653508cdd652c\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64916dec3207c110b65653508cdd652c\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fbd21e0f03e612ad3ec9e9d852e13a9\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fbd21e0f03e612ad3ec9e9d852e13a9\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9c27fde72cde99c3c3febe47f53e6f5\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9c27fde72cde99c3c3febe47f53e6f5\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ab42d11055b0e5e13e74fbbe79b2a40\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ab42d11055b0e5e13e74fbbe79b2a40\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f682cebb3b8ac7b44e608da7b3d68f64\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f682cebb3b8ac7b44e608da7b3d68f64\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03b0297fc2e61ba6168b032d13ef425b\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03b0297fc2e61ba6168b032d13ef425b\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1237673d01c0cd5458eea709a733bd\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1237673d01c0cd5458eea709a733bd\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06fc7c047fc86a5350f9661b4c75ce0\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06fc7c047fc86a5350f9661b4c75ce0\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df4047845cba141e3d72a296b8ebd827\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df4047845cba141e3d72a296b8ebd827\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9767d48e60644bdd474782a5024d9ccd\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9767d48e60644bdd474782a5024d9ccd\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba63eea34968ac021cedbd1b85bd502\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba63eea34968ac021cedbd1b85bd502\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aa52d47348985c73b8cf35e1e65e62\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aa52d47348985c73b8cf35e1e65e62\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6cd8ec49b429079797bd87a9cbd6ff3\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6cd8ec49b429079797bd87a9cbd6ff3\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe3ee9224a8196eee6ee54c26b36d74\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe3ee9224a8196eee6ee54c26b36d74\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ed4efd95d8440ba0b6c7542219c081\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ed4efd95d8440ba0b6c7542219c081\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1dfd1b5a83d588f5e603577b9178d0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1dfd1b5a83d588f5e603577b9178d0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87fa0d78838279af547bb791a0359734\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87fa0d78838279af547bb791a0359734\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99cc69e026378dbd6f90620a53792b58\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99cc69e026378dbd6f90620a53792b58\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31aae105eb04a406e2ec638f81d2b6bb\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31aae105eb04a406e2ec638f81d2b6bb\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41326908fbc54526a8c2668ccc81005d\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41326908fbc54526a8c2668ccc81005d\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2927c22491b7bae5c3e8b02cbb6dcaf\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2927c22491b7bae5c3e8b02cbb6dcaf\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58ecd19d0aa0f88f0cfc56e03515ed8\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58ecd19d0aa0f88f0cfc56e03515ed8\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004bf2b17555a1d78bc2e33d5e6648a9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004bf2b17555a1d78bc2e33d5e6648a9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0245299d52204dfeed11ab2f0057dc07\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0245299d52204dfeed11ab2f0057dc07\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635f7e61c4c8c520293b2abbd7328749\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635f7e61c4c8c520293b2abbd7328749\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dfea2d02a2f5252c44a957dbdd44a64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dfea2d02a2f5252c44a957dbdd44a64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e77cf24b40b71eb50f74e133a7bc34f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e77cf24b40b71eb50f74e133a7bc34f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\123cf6522c99c23fb07dc81c0a39f035\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\123cf6522c99c23fb07dc81c0a39f035\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7e8589b595a47af3e9ced4ba3efcd18\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7e8589b595a47af3e9ced4ba3efcd18\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddd1982f0bff2916f6b431d03c71d81\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddd1982f0bff2916f6b431d03c71d81\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b5082326aaded3911b6e6ec44be48d\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b5082326aaded3911b6e6ec44be48d\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b105516173dd17fd78ec8185e8cc5101\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b105516173dd17fd78ec8185e8cc5101\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f02e74a5d81ac387218e6b268b524af\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f02e74a5d81ac387218e6b268b524af\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30007fc325d59e5b0197043eba024cce\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30007fc325d59e5b0197043eba024cce\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8038d679c6201ccc8bf88cea2ee85a9a\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8038d679c6201ccc8bf88cea2ee85a9a\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c962283642e48ac832ef3cf069ec7d35\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c962283642e48ac832ef3cf069ec7d35\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dd958c62274c71174c6ad8e15200343\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dd958c62274c71174c6ad8e15200343\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f899b0c191eabd106cb0793f29ac337\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f899b0c191eabd106cb0793f29ac337\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2bcd653822bd9b07c43a787f3de1c7\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2bcd653822bd9b07c43a787f3de1c7\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe005d8bf59ba00e237aa64d6c04b6d\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe005d8bf59ba00e237aa64d6c04b6d\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4233f5702469b5ea3a7f7cc111e192\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4233f5702469b5ea3a7f7cc111e192\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b62bd6770a1a0bd7e3a3f25f030bff9\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b62bd6770a1a0bd7e3a3f25f030bff9\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0cd3405513bf86b8aedb79eae3d7fc5\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0cd3405513bf86b8aedb79eae3d7fc5\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99007c466855608ffe353adb75f2d342\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99007c466855608ffe353adb75f2d342\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec2c412bae5546cfe5bcb2197c2a59d\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec2c412bae5546cfe5bcb2197c2a59d\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f538b73d39080ea81e8ea047f79eeb8d\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f538b73d39080ea81e8ea047f79eeb8d\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\774f298d909cfcbae87a53f1f0621476\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\774f298d909cfcbae87a53f1f0621476\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795db646d5257b1dffa43100532574a6\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795db646d5257b1dffa43100532574a6\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257371aebd960c24715bb6abbff9f5bf\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257371aebd960c24715bb6abbff9f5bf\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e602597bb3acfa2a82782b1a6d7284c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e602597bb3acfa2a82782b1a6d7284c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2101fa1a881bd2edef289cc4182707\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2101fa1a881bd2edef289cc4182707\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec57f1f6f00a791e95b686e6b5e74c40\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec57f1f6f00a791e95b686e6b5e74c40\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\586e0e8333d5b42f64060d9d50a926dd\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\586e0e8333d5b42f64060d9d50a926dd\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a657d9f65b35d86386fed4edbd5dc299\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a657d9f65b35d86386fed4edbd5dc299\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\494d8f7885d899fb7b9b4393243be850\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\494d8f7885d899fb7b9b4393243be850\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caa2dff8b12681a2413f8bcfb00b93cd\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caa2dff8b12681a2413f8bcfb00b93cd\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecbdd3f36fbda2007ff0555db1e2d48a\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecbdd3f36fbda2007ff0555db1e2d48a\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ae22da40e9e11c25c5dbb3c54bcbdc\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ae22da40e9e11c25c5dbb3c54bcbdc\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db4f5d8937d7c4955d8faea4dddc9086\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db4f5d8937d7c4955d8faea4dddc9086\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7524487013c78eb2b8ee578ae0584de7\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7524487013c78eb2b8ee578ae0584de7\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e1fc5fcaa3ed5f5449494b4af1d023\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e1fc5fcaa3ed5f5449494b4af1d023\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f701b1c825c6e4f40e596908bba825e7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f701b1c825c6e4f40e596908bba825e7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78058efdb2a613945fc64792040e9310\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78058efdb2a613945fc64792040e9310\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8dcdc5c56667393d0b3e956be36660f\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8dcdc5c56667393d0b3e956be36660f\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\078f9f02e495f0dd0d4e161296c58ceb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\078f9f02e495f0dd0d4e161296c58ceb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37b335fa6456d069fae66478cd3f5d86\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37b335fa6456d069fae66478cd3f5d86\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bdef6d4c70169befe3abf2d5b3b6da\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bdef6d4c70169befe3abf2d5b3b6da\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee85571c65f69f6561466a11705304cf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee85571c65f69f6561466a11705304cf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8921afe01ea231b105108b56412c9411\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8921afe01ea231b105108b56412c9411\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edaac9dd15858d6bddb889f7ae2dce70\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edaac9dd15858d6bddb889f7ae2dce70\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943c5e84007847618e929643a3f16713\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943c5e84007847618e929643a3f16713\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5857038c93ce878d6caa714f85550df\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5857038c93ce878d6caa714f85550df\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a781e690c6f85408986c8e0bc88c5dbe\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a781e690c6f85408986c8e0bc88c5dbe\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed26e31f373a353d6df51f524243fa77\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed26e31f373a353d6df51f524243fa77\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e397bb62bef4ae7fa0227e458b86cc6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e397bb62bef4ae7fa0227e458b86cc6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f773416775a47656b455505e7c2d836\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f773416775a47656b455505e7c2d836\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14df64deaa65d652801af737f01a39b9\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14df64deaa65d652801af737f01a39b9\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e111718ef3632e36e79757ab187886b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e111718ef3632e36e79757ab187886b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b51e513eaac6ddc479b7ab44cbe758\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b51e513eaac6ddc479b7ab44cbe758\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aff1c1c001931892e6322b21062b5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aff1c1c001931892e6322b21062b5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2b839604b972af639853fc4fcf6ad4e\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2b839604b972af639853fc4fcf6ad4e\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ada69cf34ac3768f494c8eaa4a4a91a\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ada69cf34ac3768f494c8eaa4a4a91a\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163bb7dfa58f2e00c622862ee5c8da95\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163bb7dfa58f2e00c622862ee5c8da95\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44ebbf2f2c8625d954228a1184185d\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44ebbf2f2c8625d954228a1184185d\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37213c2832ac8fb03f8a9407c7109e91\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37213c2832ac8fb03f8a9407c7109e91\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca31741dd4b7ed918dbb9b294b453c3\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca31741dd4b7ed918dbb9b294b453c3\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ffb77c3facd0be7cf360be32700013e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ffb77c3facd0be7cf360be32700013e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\880c43895efa6facf988b03ab593d472\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\880c43895efa6facf988b03ab593d472\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e51d0b3859d8154ebe72962e057ff1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e51d0b3859d8154ebe72962e057ff1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5994bb38ec15e11bdc857c2750fe05\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5994bb38ec15e11bdc857c2750fe05\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a7f386a268f8e06f85d388d47b823ab\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a7f386a268f8e06f85d388d47b823ab\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\390a03af89b11bd85608373396a7a672\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\390a03af89b11bd85608373396a7a672\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39ab0d272159339cff56d09d2456bfd2\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39ab0d272159339cff56d09d2456bfd2\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b22009fa58ae9cfbdc13cf4633cdd1c8\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b22009fa58ae9cfbdc13cf4633cdd1c8\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c77ba46fd4f7461df3ea066903c0cf73\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c77ba46fd4f7461df3ea066903c0cf73\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f9f12d82a908e44f849c25bba51e2dc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f9f12d82a908e44f849c25bba51e2dc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48b133c067113574b2e7afe09bf975f1\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48b133c067113574b2e7afe09bf975f1\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b927f0a827a362ae4d5f43d0882c12\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b927f0a827a362ae4d5f43d0882c12\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fa3ed7ff7791d06670ff1e405faada\transformed\hermes-android-0.76.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fa3ed7ff7791d06670ff1e405faada\transformed\hermes-android-0.76.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4baf8bc0e22c795bbafca92de503ec81\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4baf8bc0e22c795bbafca92de503ec81\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b55bba363d9b043d59112d94c0d6f40\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b55bba363d9b043d59112d94c0d6f40\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da5319cab98f7711fb59418bc962b00\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da5319cab98f7711fb59418bc962b00\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348d8678be95df500a7b14340d72a3ca\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348d8678be95df500a7b14340d72a3ca\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2cefc9402a998496405605999dac44\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2cefc9402a998496405605999dac44\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70082ae80e0904f10db117483583134e\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70082ae80e0904f10db117483583134e\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f75cd99f07d3a6916f227f2ef992e672\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f75cd99f07d3a6916f227f2ef992e672\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f2bc2c05b197d6b7caa82ee3b93570\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f2bc2c05b197d6b7caa82ee3b93570\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3469e458ac0f1dd7c5828e607eb0b375\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3469e458ac0f1dd7c5828e607eb0b375\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4cc817034b22846a69e32a2a6128b4ba\transformed\viewbinding-7.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4cc817034b22846a69e32a2a6128b4ba\transformed\viewbinding-7.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a82e22a10173ac37fccabd03cddd5215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a82e22a10173ac37fccabd03cddd5215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7906fb949d3e47a55c2c9ef793df54ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7906fb949d3e47a55c2c9ef793df54ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a0a71688d7c5ff7090d2346373f2ef\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a0a71688d7c5ff7090d2346373f2ef\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d49441a16557301b02d5c0c5a8c62234\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d49441a16557301b02d5c0c5a8c62234\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\497e16e603db7b5b76461a52d25a0dda\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\497e16e603db7b5b76461a52d25a0dda\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea564785c7682172e5c76fbb4c29e3c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea564785c7682172e5c76fbb4c29e3c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20a2db9ec5e7f48674b6f3c79a8b161\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20a2db9ec5e7f48674b6f3c79a8b161\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43b68f82e3f97f4754b1a9793089d94\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43b68f82e3f97f4754b1a9793089d94\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4207dae3cc02f88b436ccf4c397fbb6\transformed\rxandroid-2.0.2\AndroidManifest.xml:19:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4207dae3cc02f88b436ccf4c397fbb6\transformed\rxandroid-2.0.2\AndroidManifest.xml:19:5-43
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7e84112132efeaf5c10fc1fdd5671\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7e84112132efeaf5c10fc1fdd5671\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba63eea34968ac021cedbd1b85bd502\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
meta-data#in_app_messaging_auto_collection_enabled
ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:36
	android:value
		ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-33
	android:name
		ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
uses-permission#android.permission.WAKE_LOCK
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
receiver#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
	android:permission
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
meta-data#delivery_metrics_exported_to_big_query_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
meta-data#firebase_messaging_auto_init_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
meta-data#firebase_messaging_notification_delegation_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
	android:resource
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:19:9-28:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:19:9-28:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163c8bd99888b91071cf8fae9cc7ca51\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:20:13-77
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afc5041864a83e3da7a8b1abdad0942b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:11:9-15:18
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayKtxRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:22:13-24:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:24:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:23:17-147
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:26:17-144
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:15:17-132
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:18:17-129
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
