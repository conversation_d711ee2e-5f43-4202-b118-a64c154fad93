<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="notification_action_color_filter">@color/androidx_core_secondary_text_default_material_light</color>
    <item format="float" name="m3_sys_state_dragged_state_layer_opacity" type="dimen">0.32</item>
    <item format="float" name="m3_sys_state_focus_state_layer_opacity" type="dimen">0.24</item>
    <item format="float" name="m3_sys_state_hover_state_layer_opacity" type="dimen">0.16</item>
    <item format="float" name="m3_sys_state_pressed_state_layer_opacity" type="dimen">0.24</item>
    <dimen name="mtrl_calendar_pre_l_text_clip_padding">0dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_offset">1dp</dimen>
    <item format="float" name="mtrl_high_ripple_default_alpha" type="dimen">0.48</item>
    <item format="float" name="mtrl_high_ripple_focused_alpha" type="dimen">0.48</item>
    <item format="float" name="mtrl_high_ripple_hovered_alpha" type="dimen">0.16</item>
    <item format="float" name="mtrl_high_ripple_pressed_alpha" type="dimen">0.48</item>
    <item format="float" name="mtrl_low_ripple_default_alpha" type="dimen">0.24</item>
    <item format="float" name="mtrl_low_ripple_focused_alpha" type="dimen">0.24</item>
    <item format="float" name="mtrl_low_ripple_hovered_alpha" type="dimen">0.08</item>
    <item format="float" name="mtrl_low_ripple_pressed_alpha" type="dimen">0.24</item>
    <dimen name="notification_content_margin_start">0dp</dimen>
    <dimen name="notification_main_column_padding_top">0dp</dimen>
    <dimen name="notification_media_narrow_margin">12dp</dimen>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance.Material"/>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="android:TextAppearance.Material.Body1"/>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="android:TextAppearance.Material.Body2"/>
    <style name="Base.TextAppearance.AppCompat.Button" parent="android:TextAppearance.Material.Button"/>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="android:TextAppearance.Material.Caption"/>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="android:TextAppearance.Material.Display1"/>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="android:TextAppearance.Material.Display2"/>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="android:TextAppearance.Material.Display3"/>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="android:TextAppearance.Material.Display4"/>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="android:TextAppearance.Material.Headline"/>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="android:TextAppearance.Material.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Large" parent="android:TextAppearance.Material.Large"/>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="android:TextAppearance.Material.Large.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="android:TextAppearance.Material.Medium"/>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="android:TextAppearance.Material.Medium.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="android:TextAppearance.Material.Menu"/>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="android:TextAppearance.Material.SearchResult.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="android:TextAppearance.Material.SearchResult.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="android:TextAppearance.Material.Small"/>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="android:TextAppearance.Material.Small.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="android:TextAppearance.Material.Subhead"/>
    <style name="Base.TextAppearance.AppCompat.Title" parent="android:TextAppearance.Material.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="android:TextAppearance.Material.Widget.ActionMode.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="android:TextAppearance.Material.Widget.ActionMode.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="android:TextAppearance.Material.Widget.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="android:TextAppearance.Material.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="android:TextAppearance.Material.Widget.TextView.SpinnerItem"/>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V21.Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V21.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V21.Theme.AppCompat.Light"/>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V21.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.MaterialComponents" parent="Base.V21.Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="Base.V21.Theme.MaterialComponents.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Light" parent="Base.V21.Theme.MaterialComponents.Light"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="Base.V21.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V21.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.Material3.BottomSheetDialog" parent="Base.V21.ThemeOverlay.Material3.BottomSheetDialog"/>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog">
    <item name="android:windowBackground">@drawable/mtrl_dialog_background</item>
  </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" parent="@android:style/Theme.Material.Dialog.Alert">
    <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" parent="@android:style/Theme.Material.Light.Dialog.Alert">
    <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.V21.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents">
    <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework</item>
  </style>
    <style name="Base.V21.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog">
    <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework</item>
  </style>
    <style name="Base.V21.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light">
    <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework</item>
  </style>
    <style name="Base.V21.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog">
    <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework</item>
  </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.Material3.BottomSheetDialog" parent="Base.V14.ThemeOverlay.Material3.BottomSheetDialog">
    <item name="android:statusBarColor">@android:color/transparent</item>
  </style>
    <style name="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog">
    <item name="android:statusBarColor">@android:color/transparent</item>
  </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="android:Widget.Material.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="android:Widget.Material.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="android:Widget.Material.ActionButton">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="android:Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="android:Widget.Material.ActionButton.Overflow">
        <item name="android:src">@null</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.Material.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget.Material.Button"/>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="android:Widget.Material.Button.Borderless"/>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="android:Widget.Material.Button.Borderless.Colored">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="android:Widget.Material.Button.Small"/>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget.Material.ButtonBar"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.Material.CompoundButton.CheckBox"/>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.Material.CompoundButton.RadioButton"/>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="android:Widget.Material.DropDownItem.Spinner"/>
    <style name="Base.Widget.AppCompat.EditText" parent="android:Widget.Material.EditText">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.Material.ImageButton"/>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="android:Widget.Material.Light.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="android:Widget.Material.Light.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="android:Widget.Material.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.Material.ListView"/>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="android:Widget.Material.ListView.DropDown"/>
    <style name="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="android:Widget.Material.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Material.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Material.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.Material.RatingBar"/>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget.Material.SeekBar"/>
    <style name="Base.Widget.AppCompat.Spinner" parent="android:Widget.Material.Spinner"/>
    <style name="Base.Widget.AppCompat.TextView" parent="android:Widget.Material.TextView"/>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.Material.TextView.SpinnerItem"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget.Material.Toolbar.Button.Navigation">
    </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView" parent="android:Widget.Material.CheckedTextView"/>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" parent="Widget.AppCompat.PopupMenu">
    <item name="android:overlapAnchor">true</item>
  </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow">
      <item name="android:popupBackground">?attr/popupMenuBackground</item>
      <item name="android:popupElevation">8dp</item>
      <item name="android:dropDownVerticalOffset">1dp</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    <item name="android:datePickerMode">calendar</item>
    <item name="android:calendarTextColor">?attr/colorOnSurface</item>
    <item name="android:headerBackground">?attr/colorPrimary</item>
    <item name="android:headerMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
    <item name="android:headerDayOfMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day</item>
    <item name="android:headerYearTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    <item name="android:datePickerMode">spinner</item>
  </style>
    <style name="Platform.AppCompat" parent="Platform.V21.AppCompat"/>
    <style name="Platform.AppCompat.Light" parent="Platform.V21.AppCompat.Light"/>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark"/>
    <style name="Platform.ThemeOverlay.AppCompat.Light"/>
    <style name="Platform.V21.AppCompat" parent="android:Theme.Material.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style>
    <style name="Platform.V21.AppCompat.Light" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification"/>
    <style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Info.Media">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Media">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time"/>
    <style name="TextAppearance.Compat.Notification.Time.Media">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media">
        <item name="android:textColor">@color/primary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyLarge" parent="TextAppearance.AppCompat.Body2">
    <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.03125</item>
    <item name="lineHeight">24sp</item>
    <item name="android:lineHeight">24sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyMedium" parent="TextAppearance.AppCompat.Body1">
    <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.01785714</item>
    <item name="lineHeight">20sp</item>
    <item name="android:lineHeight">20sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodySmall" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.03333333</item>
    <item name="lineHeight">16sp</item>
    <item name="android:lineHeight">16sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayLarge" parent="TextAppearance.AppCompat.Display3">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">57sp</item>
    <item name="android:letterSpacing">-0.00438596</item>
    <item name="lineHeight">64sp</item>
    <item name="android:lineHeight">64sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayMedium" parent="TextAppearance.AppCompat.Display2">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">45sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">52sp</item>
    <item name="android:lineHeight">52sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplaySmall" parent="TextAppearance.AppCompat.Display1">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">36sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">44sp</item>
    <item name="android:lineHeight">44sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" parent="TextAppearance.AppCompat.Display1">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">32sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">40sp</item>
    <item name="android:lineHeight">40sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" parent="TextAppearance.AppCompat.Headline">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">28sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">36sp</item>
    <item name="android:lineHeight">36sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" parent="TextAppearance.AppCompat.Title">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">24sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">32sp</item>
    <item name="android:lineHeight">32sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelLarge" parent="TextAppearance.AppCompat.Body1">
    <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.00714286</item>
    <item name="lineHeight">20sp</item>
    <item name="android:lineHeight">20sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelMedium" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.04166667</item>
    <item name="lineHeight">16sp</item>
    <item name="android:lineHeight">16sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelSmall" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">11sp</item>
    <item name="android:letterSpacing">0.04545455</item>
    <item name="lineHeight">16sp</item>
    <item name="android:lineHeight">16sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleLarge" parent="TextAppearance.AppCompat.Title">
    <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">22sp</item>
    <item name="android:letterSpacing">0</item>
    <item name="lineHeight">28sp</item>
    <item name="android:lineHeight">28sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleMedium" parent="TextAppearance.AppCompat.Subhead">
    <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.009375</item>
    <item name="lineHeight">24sp</item>
    <item name="android:lineHeight">24sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleSmall" parent="TextAppearance.AppCompat.Subhead">
    <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.00714286</item>
    <item name="lineHeight">20sp</item>
    <item name="android:lineHeight">20sp</item>
    <item name="android:textAllCaps">false</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Badge" parent="Base.TextAppearance.MaterialComponents.Badge">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">normal</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="Base.TextAppearance.MaterialComponents.Button">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">normal</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="Base.TextAppearance.MaterialComponents.Headline6">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">normal</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="Base.TextAppearance.MaterialComponents.Subtitle2">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">normal</item>
  </style>
    <style name="ThemeOverlay.Material3.Dialog.Alert.Framework" parent="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework">
    <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
    <item name="android:buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="ThemeOverlay.Material3.Light.Dialog.Alert.Framework" parent="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework">
    <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
    <item name="android:buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" parent="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework"/>
    <style name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" parent="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="android:buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="android:buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar">
    <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" parent="TextAppearance.MaterialComponents.Headline6">
    <item name="android:textColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" parent="TextAppearance.MaterialComponents.Headline1">
    <item name="android:textColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner">
    <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner</item>
  </style>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
    </style>
    <style name="Widget.Material3.TabLayout" parent="Base.Widget.Material3.TabLayout">
    <item name="android:background">@drawable/m3_tabs_background</item>
  </style>
    <style name="Widget.Material3.TabLayout.Secondary" parent="Base.Widget.Material3.TabLayout.Secondary">
    <item name="android:background">@drawable/m3_tabs_background</item>
  </style>
    <style name="Widget.MaterialComponents.PopupMenu" parent="Base.Widget.MaterialComponents.PopupMenu">
    <item name="android:popupBackground">?attr/popupMenuBackground</item>
    <item name="android:popupElevation">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.PopupMenu.Overflow" parent="Base.Widget.MaterialComponents.PopupMenu.Overflow">
    <item name="android:popupBackground">?attr/popupMenuBackground</item>
    <item name="android:popupElevation">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.Slider" parent="Base.Widget.MaterialComponents.Slider">
    <item name="android:background">?attr/controlBackground</item>
  </style>
</resources>