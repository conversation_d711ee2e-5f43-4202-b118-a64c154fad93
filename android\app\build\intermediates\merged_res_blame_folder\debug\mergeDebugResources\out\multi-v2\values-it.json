{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4454,4559,4710,4835,4943,5101,5229,5349,5589,5746,5853,6007,6134,6290,6471,6538,6599", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "4554,4705,4830,4938,5096,5224,5344,5448,5741,5848,6002,6129,6285,6466,6533,6594,6671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,47,78,85,86,102,103,143,144,147,150,151,152,154,155,157,159,160,162,165,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2967,4370,7762,8244,8316,9569,9649,12914,13004,13257,13512,13598,13675,13831,13910,14057,14197,14266,14457,14679,14881", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3032,4449,7828,8311,8399,9644,9728,12999,13080,13340,13593,13670,13750,13905,13980,14122,14261,14351,14527,14755,14963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "35,36,37,38,39,40,41,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3114,3212,3314,3413,3515,3624,3731,14356", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3207,3309,3408,3510,3619,3726,3856,14452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "45,46,70,71,72,87,88,137,138,145,146,149,153,156,158,163,164,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4183,4283,7005,7103,7203,8404,8483,12462,12555,13085,13169,13427,13755,13985,14127,14532,14610,14760", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "4278,4365,7098,7198,7285,8478,8584,12550,12645,13164,13252,13507,13826,14052,14192,14605,14674,14876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6676,7451,7549,7659", "endColumns": "99,97,109,102", "endOffsets": "6771,7544,7654,7757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "168,169", "startColumns": "4,4", "startOffsets": "14968,15066", "endColumns": "97,98", "endOffsets": "15061,15160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,300,399,539,622,688,783,868,930,1018,1087,1150,1223,1286,1340,1461,1518,1580,1634,1711,1848,1933,2015,2120,2201,2282,2373,2440,2506,2579,2659,2750,2825,2902,2971,3048,3136,3225,3318,3411,3485,3565,3659,3710,3776,3860,3948,4010,4074,4137,4252,4362,4468,4577", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "218,295,394,534,617,683,778,863,925,1013,1082,1145,1218,1281,1335,1456,1513,1575,1629,1706,1843,1928,2010,2115,2196,2277,2368,2435,2501,2574,2654,2745,2820,2897,2966,3043,3131,3220,3313,3406,3480,3560,3654,3705,3771,3855,3943,4005,4069,4132,4247,4357,4463,4572,4652"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3037,3861,3960,4100,7290,7356,8159,8589,8651,8739,8808,8871,8944,9007,9061,9182,9239,9301,9355,9432,9733,9818,9900,10005,10086,10167,10258,10325,10391,10464,10544,10635,10710,10787,10856,10933,11021,11110,11203,11296,11370,11450,11544,11595,11661,11745,11833,11895,11959,12022,12137,12247,12353,12650", "endLines": "5,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,139", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "268,3109,3955,4095,4178,7351,7446,8239,8646,8734,8803,8866,8939,9002,9056,9177,9234,9296,9350,9427,9564,9813,9895,10000,10081,10162,10253,10320,10386,10459,10539,10630,10705,10782,10851,10928,11016,11105,11198,11291,11365,11445,11539,11590,11656,11740,11828,11890,11954,12017,12132,12242,12348,12457,12725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78d8ce858a08fb06de78ffa9e22ca9ac\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,226,284,333,411,485,557,610,667,738", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "174,221,279,328,406,480,552,605,662,733,789"}, "to": {"startLines": "67,68,69,79,80,81,82,83,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6776,6900,6947,7833,7882,7960,8034,8106,12730,12787,12858", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "6895,6942,7000,7877,7955,8029,8101,8154,12782,12853,12909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,378,481,590,674,779,898,976,1051,1143,1237,1330,1424,1525,1619,1716,1811,1903,1995,2076,2182,2289,2387,2491,2597,2704,2867,13345", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "373,476,585,669,774,893,971,1046,1138,1232,1325,1419,1520,1614,1711,1806,1898,1990,2071,2177,2284,2382,2486,2592,2699,2862,2962,13422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5453", "endColumns": "135", "endOffsets": "5584"}}]}]}