import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from 'events';

class MessageService extends EventEmitter {
  constructor() {
    super();
    this.messages = [];
    this.conversations = [];
    this.unreadCount = 0;
    this.initialized = false;
  }

  async initialize() {
    try {
      console.log('💬 Initializing Message Service...');
      await this.loadMessages();
      await this.loadConversations();
      this.initialized = true;
      console.log('✅ Message service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize message service:', error);
    }
  }

  // Load messages from storage
  async loadMessages() {
    try {
      const storedMessages = await AsyncStorage.getItem('app_messages');
      if (storedMessages) {
        this.messages = JSON.parse(storedMessages);
        this.calculateUnreadCount();
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  }

  // Load conversations from storage
  async loadConversations() {
    try {
      const storedConversations = await AsyncStorage.getItem('app_conversations');
      if (storedConversations) {
        this.conversations = JSON.parse(storedConversations);
      } else {
        // Create default conversations
        this.conversations = [
          {
            id: 'support',
            title: 'Customer Support',
            subtitle: 'Get help with your account',
            avatar: 'headset',
            type: 'support',
            lastMessage: null,
            lastMessageTime: null,
            unreadCount: 0,
          },
          {
            id: 'notifications',
            title: 'Bank Notifications',
            subtitle: 'Important updates and alerts',
            avatar: 'bank',
            type: 'system',
            lastMessage: null,
            lastMessageTime: null,
            unreadCount: 0,
          },
          {
            id: 'transactions',
            title: 'Transaction Alerts',
            subtitle: 'Payment and transfer confirmations',
            avatar: 'credit-card',
            type: 'transaction',
            lastMessage: null,
            lastMessageTime: null,
            unreadCount: 0,
          }
        ];
        await this.saveConversations();
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  }

  // Save messages to storage
  async saveMessages() {
    try {
      await AsyncStorage.setItem('app_messages', JSON.stringify(this.messages));
    } catch (error) {
      console.error('Error saving messages:', error);
    }
  }

  // Save conversations to storage
  async saveConversations() {
    try {
      await AsyncStorage.setItem('app_conversations', JSON.stringify(this.conversations));
    } catch (error) {
      console.error('Error saving conversations:', error);
    }
  }

  // Add a new message
  async addMessage(messageData) {
    const message = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      conversationId: messageData.conversationId || 'notifications',
      title: messageData.title || '',
      body: messageData.body || '',
      type: messageData.type || 'info', // info, success, warning, error, transaction
      timestamp: new Date().toISOString(),
      isRead: false,
      data: messageData.data || {},
      sender: messageData.sender || 'system', // system, user, support
      avatar: messageData.avatar || null,
    };

    this.messages.unshift(message);
    await this.saveMessages();

    // Update conversation
    await this.updateConversation(message.conversationId, message);

    this.calculateUnreadCount();
    this.emit('messageAdded', message);
    this.emit('unreadCountChanged', this.unreadCount);

    console.log('📨 New message added:', message.title);
    return message;
  }

  // Update conversation with latest message
  async updateConversation(conversationId, message) {
    const conversationIndex = this.conversations.findIndex(c => c.id === conversationId);
    if (conversationIndex !== -1) {
      this.conversations[conversationIndex].lastMessage = message.body;
      this.conversations[conversationIndex].lastMessageTime = message.timestamp;
      if (!message.isRead) {
        this.conversations[conversationIndex].unreadCount += 1;
      }
      await this.saveConversations();
      this.emit('conversationsUpdated', this.conversations);
    }
  }

  // Get messages for a specific conversation
  getMessagesForConversation(conversationId) {
    return this.messages
      .filter(message => message.conversationId === conversationId)
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }

  // Get all conversations
  getConversations() {
    return this.conversations.sort((a, b) => {
      const timeA = a.lastMessageTime ? new Date(a.lastMessageTime) : new Date(0);
      const timeB = b.lastMessageTime ? new Date(b.lastMessageTime) : new Date(0);
      return timeB - timeA;
    });
  }

  // Mark messages as read
  async markMessagesAsRead(conversationId) {
    let hasChanges = false;

    this.messages.forEach(message => {
      if (message.conversationId === conversationId && !message.isRead) {
        message.isRead = true;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      await this.saveMessages();

      // Update conversation unread count
      const conversationIndex = this.conversations.findIndex(c => c.id === conversationId);
      if (conversationIndex !== -1) {
        this.conversations[conversationIndex].unreadCount = 0;
        await this.saveConversations();
      }

      this.calculateUnreadCount();
      this.emit('messagesRead', conversationId);
      this.emit('unreadCountChanged', this.unreadCount);
      this.emit('conversationsUpdated', this.conversations);
    }
  }

  // Calculate total unread count
  calculateUnreadCount() {
    this.unreadCount = this.messages.filter(message => !message.isRead).length;
  }

  // Get unread count
  getUnreadCount() {
    return this.unreadCount;
  }

  // Delete a message
  async deleteMessage(messageId) {
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex !== -1) {
      const message = this.messages[messageIndex];
      this.messages.splice(messageIndex, 1);
      await this.saveMessages();

      // Update conversation if this was the last message
      const conversationMessages = this.getMessagesForConversation(message.conversationId);
      const conversation = this.conversations.find(c => c.id === message.conversationId);

      if (conversation) {
        if (conversationMessages.length > 0) {
          const lastMessage = conversationMessages[conversationMessages.length - 1];
          conversation.lastMessage = lastMessage.body;
          conversation.lastMessageTime = lastMessage.timestamp;
        } else {
          conversation.lastMessage = null;
          conversation.lastMessageTime = null;
        }

        if (!message.isRead) {
          conversation.unreadCount = Math.max(0, conversation.unreadCount - 1);
        }

        await this.saveConversations();
      }

      this.calculateUnreadCount();
      this.emit('messageDeleted', messageId);
      this.emit('unreadCountChanged', this.unreadCount);
      this.emit('conversationsUpdated', this.conversations);
    }
  }

  // Clear all messages for a conversation
  async clearConversation(conversationId) {
    this.messages = this.messages.filter(message => message.conversationId !== conversationId);
    await this.saveMessages();

    // Reset conversation
    const conversation = this.conversations.find(c => c.id === conversationId);
    if (conversation) {
      conversation.lastMessage = null;
      conversation.lastMessageTime = null;
      conversation.unreadCount = 0;
      await this.saveConversations();
    }

    this.calculateUnreadCount();
    this.emit('conversationCleared', conversationId);
    this.emit('unreadCountChanged', this.unreadCount);
    this.emit('conversationsUpdated', this.conversations);
  }

  // Handle FCM message and convert to in-app message
  async handleFCMMessage(remoteMessage) {
    const messageType = remoteMessage.data?.messageType || 'notification';
    const conversationId = remoteMessage.data?.conversationId || 'notifications';

    const messageData = {
      conversationId,
      title: remoteMessage.notification?.title || 'SafariBank',
      body: remoteMessage.notification?.body || 'You have a new notification',
      type: messageType,
      data: remoteMessage.data || {},
      sender: 'system',
    };

    return await this.addMessage(messageData);
  }

  // Send a user message (for support chat)
  async sendUserMessage(conversationId, messageText) {
    const messageData = {
      conversationId,
      title: '',
      body: messageText,
      type: 'user',
      sender: 'user',
    };

    return await this.addMessage(messageData);
  }

  // Get message statistics
  getStats() {
    return {
      totalMessages: this.messages.length,
      unreadMessages: this.unreadCount,
      conversations: this.conversations.length,
      messagesByType: this.messages.reduce((acc, message) => {
        acc[message.type] = (acc[message.type] || 0) + 1;
        return acc;
      }, {}),
    };
  }
}

// Create singleton instance
const messageService = new MessageService();

export default messageService;