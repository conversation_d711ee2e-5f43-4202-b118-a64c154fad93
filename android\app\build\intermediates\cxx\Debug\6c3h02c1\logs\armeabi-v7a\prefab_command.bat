@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging6203702384969426062\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\prefab" ^
  "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\6q3c2t5q" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\74fa3ed7ff7791d06670ff1e405faada\\transformed\\hermes-android-0.76.6-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d20a2db9ec5e7f48674b6f3c79a8b161\\transformed\\fbjni-0.6.0\\prefab"
