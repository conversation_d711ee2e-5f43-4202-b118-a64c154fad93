{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,336,420,517,654,746,812,911,988,1051,1169,1234,1291,1361,1422,1476,1592,1649,1711,1765,1839,1967,2055,2141,2248,2332,2417,2508,2575,2641,2713,2791,2887,2967,3043,3120,3197,3286,3359,3449,3544,3618,3699,3792,3847,3913,3999,4084,4146,4210,4273,4371,4471,4566,4668", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "331,415,512,649,741,807,906,983,1046,1164,1229,1286,1356,1417,1471,1587,1644,1706,1760,1834,1962,2050,2136,2243,2327,2412,2503,2570,2636,2708,2786,2882,2962,3038,3115,3192,3281,3354,3444,3539,3613,3694,3787,3842,3908,3994,4079,4141,4205,4268,4366,4466,4561,4663,4743"}, "to": {"startLines": "2,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3177,3988,4085,4222,7252,7318,7813,8226,8358,8476,8541,8598,8668,8729,8783,8899,8956,9018,9072,9146,9497,9585,9671,9778,9862,9947,10038,10105,10171,10243,10321,10417,10497,10573,10650,10727,10816,10889,10979,11074,11148,11229,11322,11377,11443,11529,11614,11676,11740,11803,11901,12001,12096,12368", "endLines": "7,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "381,3256,4080,4217,4309,7313,7412,7885,8284,8471,8536,8593,8663,8724,8778,8894,8951,9013,9067,9141,9269,9580,9666,9773,9857,9942,10033,10100,10166,10238,10316,10412,10492,10568,10645,10722,10811,10884,10974,11069,11143,11224,11317,11372,11438,11524,11609,11671,11735,11798,11896,11996,12091,12193,12443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "386,501,603,702,788,893,1014,1093,1169,1261,1355,1450,1543,1638,1732,1828,1923,2015,2107,2196,2302,2409,2507,2616,2723,2837,3003,12961", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "496,598,697,783,888,1009,1088,1164,1256,1350,1445,1538,1633,1727,1823,1918,2010,2102,2191,2297,2404,2502,2611,2718,2832,2998,3098,13038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4579,4686,4852,4978,5088,5230,5359,5474,5735,5916,6023,6186,6312,6479,6637,6706,6766", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4681,4847,4973,5083,5225,5354,5469,5573,5911,6018,6181,6307,6474,6632,6701,6761,6847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5578", "endColumns": "156", "endOffsets": "5730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "37,38,39,40,41,42,43,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3261,3359,3461,3562,3663,3768,3871,13987", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3354,3456,3557,3658,3763,3866,3983,14083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,999,1086,1158,1234,1312,1388,1472,1542", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,994,1081,1153,1229,1307,1383,1467,1537,1660"}, "to": {"startLines": "47,48,69,70,71,81,82,133,134,138,139,143,147,150,152,157,158,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4314,4407,6960,7058,7160,8054,8136,12198,12286,12634,12718,13043,13365,13601,13751,14160,14244,14394", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "4402,4485,7053,7155,7247,8131,8221,12281,12363,12713,12800,13110,13436,13674,13822,14239,14309,14512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6852,7417,7522,7634", "endColumns": "107,104,111,104", "endOffsets": "6955,7517,7629,7734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,292,367,456,525,592,669,748,837,934,1006,1090,1183,1258,1340,1423,1500,1572,1647,1732,1804,1884,1954", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "124,213,287,362,451,520,587,664,743,832,929,1001,1085,1178,1253,1335,1418,1495,1567,1642,1727,1799,1879,1949,2034"}, "to": {"startLines": "35,49,77,79,80,84,97,98,99,136,137,140,141,144,145,146,148,149,151,153,154,156,159,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3103,4490,7739,7890,7965,8289,9274,9341,9418,12448,12537,12805,12877,13115,13208,13283,13441,13524,13679,13827,13902,14088,14314,14517,14587", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "3172,4574,7808,7960,8049,8353,9336,9413,9492,12532,12629,12872,12956,13203,13278,13360,13519,13596,13746,13897,13982,14155,14389,14582,14667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "14672,14760", "endColumns": "87,90", "endOffsets": "14755,14846"}}]}]}