{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "13901,13988", "endColumns": "86,87", "endOffsets": "13983,14071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,319,396,489,602,682,747,835,905,968,1060,1120,1179,1242,1303,1362,1464,1521,1580,1638,1706,1817,1898,1980,2082,2153,2226,2314,2381,2447,2520,2596,2682,2752,2827,2909,2977,3062,3132,3222,3313,3387,3460,3549,3600,3667,3749,3834,3896,3960,4023,4117,4212,4302,4398", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "314,391,484,597,677,742,830,900,963,1055,1115,1174,1237,1298,1357,1459,1516,1575,1633,1701,1812,1893,1975,2077,2148,2221,2309,2376,2442,2515,2591,2677,2747,2822,2904,2972,3057,3127,3217,3308,3382,3455,3544,3595,3662,3744,3829,3891,3955,4018,4112,4207,4297,4393,4468"}, "to": {"startLines": "2,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3094,3868,3961,4074,6838,6903,7369,7751,7882,7974,8034,8093,8156,8217,8276,8378,8435,8494,8552,8620,8955,9036,9118,9220,9291,9364,9452,9519,9585,9658,9734,9820,9890,9965,10047,10115,10200,10270,10360,10451,10525,10598,10687,10738,10805,10887,10972,11034,11098,11161,11255,11350,11440,11701", "endLines": "7,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "364,3166,3956,4069,4149,6898,6986,7434,7809,7969,8029,8088,8151,8212,8271,8373,8430,8489,8547,8615,8726,9031,9113,9215,9286,9359,9447,9514,9580,9653,9729,9815,9885,9960,10042,10110,10195,10265,10355,10446,10520,10593,10682,10733,10800,10882,10967,11029,11093,11156,11250,11345,11435,11531,11771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,954,1036,1105,1179,1257,1333,1407,1478", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,949,1031,1100,1174,1252,1328,1402,1473,1592"}, "to": {"startLines": "47,48,69,70,71,81,82,133,134,138,139,143,147,150,152,157,158,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4154,4243,6567,6660,6755,7589,7666,11536,11622,11937,12015,12328,12630,12857,13007,13414,13488,13638", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "4238,4322,6655,6750,6833,7661,7746,11617,11696,12010,12092,12392,12699,12930,13078,13483,13554,13752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "35,49,77,79,80,84,97,98,99,136,137,140,141,144,145,146,148,149,151,153,154,156,159,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,4327,7299,7439,7508,7814,8731,8799,8877,11776,11858,12097,12168,12397,12477,12550,12704,12782,12935,13083,13155,13343,13559,13757,13826", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3089,4400,7364,7503,7584,7877,8794,8872,8950,11853,11932,12163,12241,12472,12545,12625,12777,12852,13002,13150,13237,13409,13633,13821,13896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "37,38,39,40,41,42,43,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3265,3367,3464,3561,3662,3762,13242", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3260,3362,3459,3556,3657,3757,3863,13338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6476,6991,7091,7197", "endColumns": "90,99,105,101", "endOffsets": "6562,7086,7192,7294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "369,474,574,682,766,868,984,1063,1141,1232,1326,1420,1514,1614,1707,1802,1895,1986,2078,2159,2264,2367,2465,2570,2672,2774,2928,12246", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "469,569,677,761,863,979,1058,1136,1227,1321,1415,1509,1609,1702,1797,1890,1981,2073,2154,2259,2362,2460,2565,2667,2769,2923,3020,12323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5370", "endColumns": "117", "endOffsets": "5483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4405,4508,4662,4787,4891,5030,5155,5267,5488,5624,5728,5873,5996,6130,6275,6335,6395", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4503,4657,4782,4886,5025,5150,5262,5365,5619,5723,5868,5991,6125,6270,6330,6390,6471"}}]}]}