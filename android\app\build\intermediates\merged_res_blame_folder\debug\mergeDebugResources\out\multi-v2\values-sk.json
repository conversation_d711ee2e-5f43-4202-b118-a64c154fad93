{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "37,38,39,40,41,42,43,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3248,3344,3446,3547,3645,3755,3863,13860", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3339,3441,3542,3640,3750,3858,3980,13956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "383,490,591,702,788,896,1014,1093,1170,1261,1354,1452,1546,1646,1739,1834,1932,2023,2114,2198,2303,2411,2510,2616,2728,2831,2997,12841", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "485,586,697,783,891,1009,1088,1165,1256,1349,1447,1541,1641,1734,1829,1927,2018,2109,2193,2298,2406,2505,2611,2723,2826,2992,3090,12919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "47,48,69,70,71,81,82,133,134,138,139,143,147,150,152,157,158,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4286,4381,6922,7017,7120,8001,8080,12079,12169,12511,12594,12924,13238,13476,13627,14032,14110,14257", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "4376,4460,7012,7115,7207,8075,8169,12164,12245,12589,12676,12991,13311,13547,13697,14105,14173,14366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5547", "endColumns": "138", "endOffsets": "5681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "35,49,77,79,80,84,97,98,99,136,137,140,141,144,145,146,148,149,151,153,154,156,159,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3095,4465,7696,7843,7914,8239,9195,9264,9345,12329,12416,12681,12755,12996,13080,13157,13316,13398,13552,13702,13776,13961,14178,14371,14442", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3165,4543,7762,7909,7996,8302,9259,9340,9421,12411,12506,12750,12836,13075,13152,13233,13393,13471,13622,13771,13855,14027,14252,14437,14520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "14525,14609", "endColumns": "83,86", "endOffsets": "14604,14691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "68,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6815,7376,7479,7594", "endColumns": "106,102,114,101", "endOffsets": "6917,7474,7589,7691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,333,411,503,631,712,777,876,952,1017,1107,1173,1227,1296,1356,1410,1527,1587,1649,1703,1775,1905,1992,2084,2193,2262,2340,2428,2495,2561,2633,2710,2793,2865,2942,3015,3086,3174,3246,3338,3434,3508,3582,3678,3730,3797,3884,3971,4033,4097,4160,4266,4362,4460,4558", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "328,406,498,626,707,772,871,947,1012,1102,1168,1222,1291,1351,1405,1522,1582,1644,1698,1770,1900,1987,2079,2188,2257,2335,2423,2490,2556,2628,2705,2788,2860,2937,3010,3081,3169,3241,3333,3429,3503,3577,3673,3725,3792,3879,3966,4028,4092,4155,4261,4357,4455,4553,4632"}, "to": {"startLines": "2,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3170,3985,4077,4205,7212,7277,7767,8174,8307,8397,8463,8517,8586,8646,8700,8817,8877,8939,8993,9065,9426,9513,9605,9714,9783,9861,9949,10016,10082,10154,10231,10314,10386,10463,10536,10607,10695,10767,10859,10955,11029,11103,11199,11251,11318,11405,11492,11554,11618,11681,11787,11883,11981,12250", "endLines": "7,36,44,45,46,72,73,78,83,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,135", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "378,3243,4072,4200,4281,7272,7371,7838,8234,8392,8458,8512,8581,8641,8695,8812,8872,8934,8988,9060,9190,9508,9600,9709,9778,9856,9944,10011,10077,10149,10226,10309,10381,10458,10531,10602,10690,10762,10854,10950,11024,11098,11194,11246,11313,11400,11487,11549,11613,11676,11782,11878,11976,12074,12324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4654,4814,4938,5048,5204,5332,5445,5686,5855,5966,6136,6266,6429,6593,6661,6728", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "4649,4809,4933,5043,5199,5327,5440,5542,5850,5961,6131,6261,6424,6588,6656,6723,6810"}}]}]}