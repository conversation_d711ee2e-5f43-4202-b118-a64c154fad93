1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.camelcase.rnstarbank"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:3-62
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:3-64
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:3-77
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:3-68
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:20-66
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:3-63
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:3-78
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:20-76
18
19    <queries>
19-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:9:3-15:13
20        <intent>
20-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
32
33            <!-- Required for picking images from the camera roll if targeting API 30 -->
34            <action android:name="android.media.action.IMAGE_CAPTURE" />
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
35        </intent>
36        <intent>
36-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
37
38            <!-- Required for picking images from the camera if targeting API 30 -->
39            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
40        </intent>
41        <intent>
41-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
42
43            <!-- Required for opening tabs if targeting API 30 -->
44            <action android:name="android.support.customtabs.action.CustomTabsService" />
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
45        </intent> <!-- Needs to be explicitly declared on Android R+ -->
46        <package android:name="com.google.android.apps.maps" />
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
47
48        <intent>
48-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
49            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
49-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
49-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
50        </intent>
51        <intent>
51-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
52            <action android:name="android.intent.action.GET_CONTENT" />
52-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
52-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
53
54            <category android:name="android.intent.category.OPENABLE" />
54-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
54-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
55
56            <data android:mimeType="*/*" />
56-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
57        </intent>
58    </queries>
59
60    <uses-permission android:name="android.permission.WAKE_LOCK" />
60-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
60-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
61    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
61-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
61-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
62    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
62-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
62-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
63    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
63-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
63-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
64
65    <uses-feature
65-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
66        android:glEsVersion="0x00020000"
66-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
67        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
67-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
68    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
68-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
68-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
69    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
69-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
69-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
70
71    <permission
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
72        android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
73        android:protectionLevel="signature" />
73-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
74
75    <uses-permission android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
76    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
76-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
76-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
77    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
78    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
79    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
80    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
81    <!-- for Samsung -->
82    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
83    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
84    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
85    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
86    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
87    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
88    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
89    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
90    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
91    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
92    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
93    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
94    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
95    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
96    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
97    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
98
99    <application
99-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-35:17
100        android:name="com.camelcase.rnstarbank.MainApplication"
100-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
101        android:allowBackup="true"
101-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
102        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
102-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
103        android:debuggable="true"
104        android:extractNativeLibs="false"
105        android:icon="@mipmap/ic_launcher"
105-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
106        android:label="@string/app_name"
106-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
107        android:roundIcon="@mipmap/ic_launcher_round"
107-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
108        android:supportsRtl="true"
108-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
109        android:theme="@style/AppTheme"
109-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
110        android:usesCleartextTraffic="true" >
110-->C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:18-53
111        <meta-data
111-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:5-114
112            android:name="com.google.android.geo.API_KEY"
112-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:16-61
113            android:value="ENTER_YOUR_GOOGLE_MAP_API_KEY_HERE" />
113-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:62-112
114        <meta-data
114-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:5-83
115            android:name="expo.modules.updates.ENABLED"
115-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:16-59
116            android:value="false" />
116-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:60-81
117        <meta-data
117-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:5-105
118            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
118-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:16-80
119            android:value="ALWAYS" />
119-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:81-103
120        <meta-data
120-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:5-99
121            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
121-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:16-79
122            android:value="0" />
122-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:80-97
123
124        <activity
124-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:5-33:16
125            android:name="com.camelcase.rnstarbank.MainActivity"
125-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:15-43
126            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
126-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:44-134
127            android:exported="true"
127-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:256-279
128            android:launchMode="singleTask"
128-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:135-166
129            android:screenOrientation="portrait"
129-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:280-316
130            android:theme="@style/Theme.App.SplashScreen"
130-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:210-255
131            android:windowSoftInputMode="adjustResize" >
131-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:167-209
132            <intent-filter>
132-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:7-25:23
133                <action android:name="android.intent.action.MAIN" />
133-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:9-60
133-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:17-58
134
135                <category android:name="android.intent.category.LAUNCHER" />
135-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:9-68
135-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:19-66
136            </intent-filter>
137            <intent-filter>
137-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:26:7-32:23
138                <action android:name="android.intent.action.VIEW" />
138-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
138-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
139
140                <category android:name="android.intent.category.DEFAULT" />
140-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:9-67
140-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:19-65
141                <category android:name="android.intent.category.BROWSABLE" />
141-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
141-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
142
143                <data android:scheme="myapp" />
143-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
143-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
144                <data android:scheme="com.camelcase.rnstarbank" />
144-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
144-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
145            </intent-filter>
146        </activity>
147
148        <uses-library
148-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:5-83
149            android:name="org.apache.http.legacy"
149-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:19-56
150            android:required="false" />
150-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:57-81
151
152        <provider
152-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
153            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
153-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
154            android:authorities="com.camelcase.rnstarbank.fileprovider"
154-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
155            android:exported="false"
155-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
156            android:grantUriPermissions="true" >
156-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
157            <meta-data
157-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
158                android:name="android.support.FILE_PROVIDER_PATHS"
158-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
159                android:resource="@xml/file_provider_paths" />
159-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
160        </provider>
161
162        <service
162-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
163            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
163-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
164            android:exported="false" />
164-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
165        <service
165-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
166            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
166-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
167            android:exported="false" >
167-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
168            <intent-filter>
168-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
169                <action android:name="com.google.firebase.MESSAGING_EVENT" />
169-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
169-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
170            </intent-filter>
171        </service>
172
173        <receiver
173-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
174            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
174-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
175            android:exported="true"
175-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
176            android:permission="com.google.android.c2dm.permission.SEND" >
176-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
177            <intent-filter>
177-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
178                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
178-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
178-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
179            </intent-filter>
180        </receiver>
181
182        <meta-data
182-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
183            android:name="delivery_metrics_exported_to_big_query_enabled"
183-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
184            android:value="false" />
184-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
185        <meta-data
185-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
186            android:name="firebase_messaging_auto_init_enabled"
186-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
187            android:value="true" />
187-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
188        <meta-data
188-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
189            android:name="firebase_messaging_notification_delegation_enabled"
189-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
190            android:value="false" />
190-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
191        <meta-data
191-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
192            android:name="com.google.firebase.messaging.default_notification_channel_id"
192-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
193            android:value="" />
193-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
194        <meta-data
194-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
195            android:name="com.google.firebase.messaging.default_notification_color"
195-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
196            android:resource="@color/white" />
196-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
197        <meta-data
197-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
198            android:name="app_data_collection_default_enabled"
198-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
199            android:value="true" />
199-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
200
201        <service
201-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
202            android:name="com.google.firebase.components.ComponentDiscoveryService"
202-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
203            android:directBootAware="true"
203-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
204            android:exported="false" >
204-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
205            <meta-data
205-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
206                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
206-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
208            <meta-data
208-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
209                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
209-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
211            <meta-data
211-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
212                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
212-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
214            <meta-data
214-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
215                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
215-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
217            <meta-data
217-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
218                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
218-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
220            <meta-data
220-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
221                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
221-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
223            <meta-data
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
224                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
224-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
226            <meta-data
226-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
227                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
227-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
229        </service>
230
231        <provider
231-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
232            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
232-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
233            android:authorities="com.camelcase.rnstarbank.reactnativefirebaseappinitprovider"
233-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
234            android:exported="false"
234-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
235            android:initOrder="99" />
235-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
236        <provider
236-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
237            android:name="expo.modules.filesystem.FileSystemFileProvider"
237-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
238            android:authorities="com.camelcase.rnstarbank.FileSystemFileProvider"
238-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
239            android:exported="false"
239-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
240            android:grantUriPermissions="true" >
240-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
241            <meta-data
241-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
242                android:name="android.support.FILE_PROVIDER_PATHS"
242-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
243                android:resource="@xml/file_system_provider_paths" />
243-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
244        </provider>
245
246        <service
246-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
247            android:name="com.google.android.gms.metadata.ModuleDependencies"
247-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
248            android:enabled="false"
248-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
249            android:exported="false" >
249-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
250            <intent-filter>
250-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
251                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
251-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
251-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
252            </intent-filter>
253
254            <meta-data
254-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
255                android:name="photopicker_activity:0:required"
255-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
256                android:value="" />
256-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
257        </service>
258
259        <activity
259-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
260            android:name="com.canhub.cropper.CropImageActivity"
260-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
261            android:exported="true"
261-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
262            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
262-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
263        <provider
263-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
264            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
264-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
265            android:authorities="com.camelcase.rnstarbank.ImagePickerFileProvider"
265-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
266            android:exported="false"
266-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
267            android:grantUriPermissions="true" >
267-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
268            <meta-data
268-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
269                android:name="android.support.FILE_PROVIDER_PATHS"
269-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
270                android:resource="@xml/image_picker_provider_paths" />
270-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
271        </provider>
272
273        <service
273-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
274            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
274-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
275            android:exported="false" >
275-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
276            <intent-filter android:priority="-1" >
276-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
277                <action android:name="com.google.firebase.MESSAGING_EVENT" />
277-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
277-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
278            </intent-filter>
279        </service>
280
281        <receiver
281-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
282            android:name="expo.modules.notifications.service.NotificationsService"
282-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
283            android:enabled="true"
283-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
284            android:exported="false" >
284-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
285            <intent-filter android:priority="-1" >
285-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
285-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
286                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
286-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
286-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
287                <action android:name="android.intent.action.BOOT_COMPLETED" />
287-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
287-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
288                <action android:name="android.intent.action.REBOOT" />
288-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
288-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
289                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
289-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
289-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
290                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
290-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
290-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
291                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
291-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
291-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
292            </intent-filter>
293        </receiver>
294
295        <activity
295-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
296            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
296-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
297            android:excludeFromRecents="true"
297-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
298            android:exported="false"
298-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
299            android:launchMode="standard"
299-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
300            android:noHistory="true"
300-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
301            android:taskAffinity=""
301-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
302            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
302-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
303
304        <meta-data
304-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
305            android:name="org.unimodules.core.AppLoader#react-native-headless"
305-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
306            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
306-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
307        <meta-data
307-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
308            android:name="com.facebook.soloader.enabled"
308-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
309            android:value="true" />
309-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
310
311        <activity
311-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:19:9-21:40
312            android:name="com.facebook.react.devsupport.DevSettingsActivity"
312-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:20:13-77
313            android:exported="false" />
313-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:21:13-37
314        <activity
314-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
315            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
315-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
316            android:excludeFromRecents="true"
316-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
317            android:exported="false"
317-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
318            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
318-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
319        <!--
320            Service handling Google Sign-In user revocation. For apps that do not integrate with
321            Google Sign-In, this service will never be started.
322        -->
323        <service
323-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
324            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
324-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
325            android:exported="true"
325-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
326            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
326-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
327            android:visibleToInstantApps="true" />
327-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
328
329        <meta-data
329-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
330            android:name="com.google.android.gms.version"
330-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
331            android:value="@integer/google_play_services_version" />
331-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
332
333        <receiver
333-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
334            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
334-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
335            android:exported="true"
335-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
336            android:permission="com.google.android.c2dm.permission.SEND" >
336-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
337            <intent-filter>
337-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
338                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
338-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
338-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
339            </intent-filter>
340
341            <meta-data
341-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
342                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
342-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
343                android:value="true" />
343-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
344        </receiver>
345        <!--
346             FirebaseMessagingService performs security checks at runtime,
347             but set to not exported to explicitly avoid allowing another app to call it.
348        -->
349        <service
349-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
350            android:name="com.google.firebase.messaging.FirebaseMessagingService"
350-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
351            android:directBootAware="true"
351-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
352            android:exported="false" >
352-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
353            <intent-filter android:priority="-500" >
353-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
354                <action android:name="com.google.firebase.MESSAGING_EVENT" />
354-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
354-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
355            </intent-filter>
356        </service>
357        <service
357-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
358            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
358-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
359            android:directBootAware="true"
359-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
360            android:exported="false" >
360-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
361            <meta-data
361-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
362                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
362-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
364            <meta-data
364-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
365                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
365-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
367            <meta-data
367-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
368                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
368-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
370        </service>
371
372        <provider
372-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
373            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
373-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
374            android:authorities="com.camelcase.rnstarbank.mlkitinitprovider"
374-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
375            android:exported="false"
375-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
376            android:initOrder="99" />
376-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
377
378        <activity
378-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
379            android:name="com.google.android.gms.common.api.GoogleApiActivity"
379-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
380            android:exported="false"
380-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
381            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
381-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
382
383        <provider
383-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
384            android:name="com.google.firebase.provider.FirebaseInitProvider"
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
385            android:authorities="com.camelcase.rnstarbank.firebaseinitprovider"
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
386            android:directBootAware="true"
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
387            android:exported="false"
387-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
388            android:initOrder="100" />
388-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
389
390        <uses-library
390-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
391            android:name="androidx.camera.extensions.impl"
391-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
392            android:required="false" />
392-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
393
394        <service
394-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
395            android:name="androidx.camera.core.impl.MetadataHolderService"
395-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
396            android:enabled="false"
396-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
397            android:exported="false" >
397-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
398            <meta-data
398-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
399                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
399-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
400                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
400-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
401        </service>
402
403        <provider
403-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
404            android:name="com.canhub.cropper.CropFileProvider"
404-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
405            android:authorities="com.camelcase.rnstarbank.cropper.fileprovider"
405-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
406            android:exported="false"
406-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
407            android:grantUriPermissions="true" >
407-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
408            <meta-data
408-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
409                android:name="android.support.FILE_PROVIDER_PATHS"
409-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
410                android:resource="@xml/library_file_paths" />
410-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
411        </provider>
412        <provider
412-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
413            android:name="androidx.startup.InitializationProvider"
413-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
414            android:authorities="com.camelcase.rnstarbank.androidx-startup"
414-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
415            android:exported="false" >
415-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
416            <meta-data
416-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
417                android:name="androidx.work.WorkManagerInitializer"
417-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
418                android:value="androidx.startup" />
418-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
419            <meta-data
419-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
420                android:name="androidx.emoji2.text.EmojiCompatInitializer"
420-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
421                android:value="androidx.startup" />
421-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
422            <meta-data
422-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
423                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
423-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
424                android:value="androidx.startup" />
424-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
425            <meta-data
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
426                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
427                android:value="androidx.startup" />
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
428        </provider>
429
430        <service
430-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
431            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
431-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
433            android:enabled="@bool/enable_system_alarm_service_default"
433-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
434            android:exported="false" />
434-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
435        <service
435-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
436            android:name="androidx.work.impl.background.systemjob.SystemJobService"
436-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
437            android:directBootAware="false"
437-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
438            android:enabled="@bool/enable_system_job_service_default"
438-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
439            android:exported="true"
439-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
440            android:permission="android.permission.BIND_JOB_SERVICE" />
440-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
441        <service
441-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
442            android:name="androidx.work.impl.foreground.SystemForegroundService"
442-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
443            android:directBootAware="false"
443-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
444            android:enabled="@bool/enable_system_foreground_service_default"
444-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
445            android:exported="false" />
445-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
446
447        <receiver
447-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
448            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
448-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
449            android:directBootAware="false"
449-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
450            android:enabled="true"
450-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
451            android:exported="false" />
451-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
452        <receiver
452-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
453            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
453-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
454            android:directBootAware="false"
454-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
455            android:enabled="false"
455-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
456            android:exported="false" >
456-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
457            <intent-filter>
457-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
458                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
458-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
458-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
459                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
459-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
459-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
460            </intent-filter>
461        </receiver>
462        <receiver
462-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
463            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
463-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
464            android:directBootAware="false"
464-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
465            android:enabled="false"
465-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
466            android:exported="false" >
466-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
467            <intent-filter>
467-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
468                <action android:name="android.intent.action.BATTERY_OKAY" />
468-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
468-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
469                <action android:name="android.intent.action.BATTERY_LOW" />
469-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
469-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
470            </intent-filter>
471        </receiver>
472        <receiver
472-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
473            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
473-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
474            android:directBootAware="false"
474-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
475            android:enabled="false"
475-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
476            android:exported="false" >
476-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
477            <intent-filter>
477-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
478                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
479                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
479-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
479-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
480            </intent-filter>
481        </receiver>
482        <receiver
482-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
483            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
483-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
484            android:directBootAware="false"
484-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
485            android:enabled="false"
485-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
486            android:exported="false" >
486-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
487            <intent-filter>
487-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
488                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
489            </intent-filter>
490        </receiver>
491        <receiver
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
492            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
493            android:directBootAware="false"
493-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
494            android:enabled="false"
494-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
495            android:exported="false" >
495-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
496            <intent-filter>
496-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
497                <action android:name="android.intent.action.BOOT_COMPLETED" />
497-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
497-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
498                <action android:name="android.intent.action.TIME_SET" />
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
499                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
500            </intent-filter>
501        </receiver>
502        <receiver
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
503            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
503-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
504            android:directBootAware="false"
504-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
505            android:enabled="@bool/enable_system_alarm_service_default"
505-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
506            android:exported="false" >
506-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
507            <intent-filter>
507-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
508                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
509            </intent-filter>
510        </receiver>
511        <receiver
511-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
512            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
513            android:directBootAware="false"
513-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
514            android:enabled="true"
514-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
515            android:exported="true"
515-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
516            android:permission="android.permission.DUMP" >
516-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
517            <intent-filter>
517-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
518                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
519            </intent-filter>
520        </receiver>
521        <receiver
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
522            android:name="androidx.profileinstaller.ProfileInstallReceiver"
522-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
523            android:directBootAware="false"
523-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
524            android:enabled="true"
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
525            android:exported="true"
525-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
526            android:permission="android.permission.DUMP" >
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
527            <intent-filter>
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
528                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
528-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
528-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
529            </intent-filter>
530            <intent-filter>
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
531                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
532            </intent-filter>
533            <intent-filter>
533-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
534                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
535            </intent-filter>
536            <intent-filter>
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
537                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
538            </intent-filter>
539        </receiver>
540
541        <service
541-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
542            android:name="androidx.room.MultiInstanceInvalidationService"
542-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
543            android:directBootAware="true"
543-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
544            android:exported="false" />
544-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
545        <service
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
546            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
547            android:exported="false" >
547-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
548            <meta-data
548-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
549                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
550                android:value="cct" />
550-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
551        </service>
552        <service
552-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
553            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
553-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
554            android:exported="false"
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
555            android:permission="android.permission.BIND_JOB_SERVICE" >
555-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
556        </service>
557
558        <receiver
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
559            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
560            android:exported="false" />
560-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
561    </application>
562
563</manifest>
