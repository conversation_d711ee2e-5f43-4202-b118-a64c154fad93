1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.camelcase.rnstarbank"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:3-62
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:3-64
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:3-77
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:3-68
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:20-66
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:3-63
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:3-78
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:20-76
18
19    <queries>
19-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:9:3-15:13
20        <intent>
20-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
32
33            <!-- Required for picking images from the camera roll if targeting API 30 -->
34            <action android:name="android.media.action.IMAGE_CAPTURE" />
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
35        </intent>
36        <intent>
36-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
37
38            <!-- Required for picking images from the camera if targeting API 30 -->
39            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
40        </intent>
41        <intent>
41-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
42
43            <!-- Required for opening tabs if targeting API 30 -->
44            <action android:name="android.support.customtabs.action.CustomTabsService" />
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
45        </intent> <!-- Needs to be explicitly declared on Android R+ -->
46        <package android:name="com.google.android.apps.maps" />
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
47
48        <intent>
48-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:11:9-15:18
49            <action android:name="android.intent.action.VIEW" />
49-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
49-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
50
51            <data android:scheme="https" />
51-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
51-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
52        </intent>
53        <intent>
53-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
54            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
54-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
54-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
55        </intent>
56        <intent>
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
57            <action android:name="android.intent.action.GET_CONTENT" />
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
58
59            <category android:name="android.intent.category.OPENABLE" />
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
60
61            <data android:mimeType="*/*" />
61-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
62        </intent>
63    </queries>
64
65    <uses-permission android:name="android.permission.WAKE_LOCK" />
65-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
65-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
66    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
66-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
66-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
67    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
67-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
67-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
68    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
68-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
68-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
69
70    <uses-feature
70-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
71        android:glEsVersion="0x00020000"
71-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
72        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
72-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
73    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
73-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
73-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
74    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
74-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
74-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
75
76    <permission
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
77        android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
77-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
78        android:protectionLevel="signature" />
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
79
80    <uses-permission android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
81    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
81-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
81-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\187a1b56e442d8c2a2b08f9a6097cb93\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
82    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
83    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
84    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
85    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
86    <!-- for Samsung -->
87    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
88    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
89    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
90    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
91    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
92    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
93    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
94    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
95    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
96    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
97    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
98    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
99    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
100    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
101    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
102    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbe22c5805dfda88099ad13bfc9853a3\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
103
104    <application
104-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-35:17
105        android:name="com.camelcase.rnstarbank.MainApplication"
105-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
106        android:allowBackup="true"
106-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
108        android:debuggable="true"
109        android:extractNativeLibs="false"
110        android:icon="@mipmap/ic_launcher"
110-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
111        android:label="@string/app_name"
111-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
112        android:roundIcon="@mipmap/ic_launcher_round"
112-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
113        android:supportsRtl="true"
113-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
114        android:theme="@style/AppTheme"
114-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
115        android:usesCleartextTraffic="true" >
115-->C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:18-53
116        <meta-data
116-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:5-114
117            android:name="com.google.android.geo.API_KEY"
117-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:16-61
118            android:value="ENTER_YOUR_GOOGLE_MAP_API_KEY_HERE" />
118-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:62-112
119        <meta-data
119-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:5-83
120            android:name="expo.modules.updates.ENABLED"
120-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:16-59
121            android:value="false" />
121-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:60-81
122        <meta-data
122-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:5-105
123            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
123-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:16-80
124            android:value="ALWAYS" />
124-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:81-103
125        <meta-data
125-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:5-99
126            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
126-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:16-79
127            android:value="0" />
127-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:80-97
128
129        <activity
129-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:5-33:16
130            android:name="com.camelcase.rnstarbank.MainActivity"
130-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:15-43
131            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
131-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:44-134
132            android:exported="true"
132-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:256-279
133            android:launchMode="singleTask"
133-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:135-166
134            android:screenOrientation="portrait"
134-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:280-316
135            android:theme="@style/Theme.App.SplashScreen"
135-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:210-255
136            android:windowSoftInputMode="adjustResize" >
136-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:167-209
137            <intent-filter>
137-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:7-25:23
138                <action android:name="android.intent.action.MAIN" />
138-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:9-60
138-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:17-58
139
140                <category android:name="android.intent.category.LAUNCHER" />
140-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:9-68
140-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:19-66
141            </intent-filter>
142            <intent-filter>
142-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:26:7-32:23
143                <action android:name="android.intent.action.VIEW" />
143-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
143-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
144
145                <category android:name="android.intent.category.DEFAULT" />
145-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:9-67
145-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:19-65
146                <category android:name="android.intent.category.BROWSABLE" />
146-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
146-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
147
148                <data android:scheme="myapp" />
148-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
148-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
149                <data android:scheme="com.camelcase.rnstarbank" />
149-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
149-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
150            </intent-filter>
151        </activity>
152
153        <uses-library
153-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:5-83
154            android:name="org.apache.http.legacy"
154-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:19-56
155            android:required="false" />
155-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:34:57-81
156
157        <provider
157-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
158            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
158-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
159            android:authorities="com.camelcase.rnstarbank.fileprovider"
159-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
160            android:exported="false"
160-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
161            android:grantUriPermissions="true" >
161-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
162            <meta-data
162-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
163                android:name="android.support.FILE_PROVIDER_PATHS"
163-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
164                android:resource="@xml/file_provider_paths" />
164-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
165        </provider>
166
167        <meta-data
167-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:36
168            android:name="in_app_messaging_auto_collection_enabled"
168-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
169            android:value="true" />
169-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-33
170
171        <service
171-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
172            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
172-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
173            android:exported="false" />
173-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
174        <service
174-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
175            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
175-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
176            android:exported="false" >
176-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
177            <intent-filter>
177-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
178                <action android:name="com.google.firebase.MESSAGING_EVENT" />
178-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
178-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
179            </intent-filter>
180        </service>
181
182        <receiver
182-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
183            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
183-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
184            android:exported="true"
184-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
185            android:permission="com.google.android.c2dm.permission.SEND" >
185-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
186            <intent-filter>
186-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
187                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
187-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
187-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
188            </intent-filter>
189        </receiver>
190
191        <meta-data
191-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
192            android:name="delivery_metrics_exported_to_big_query_enabled"
192-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
193            android:value="false" />
193-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
194        <meta-data
194-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
195            android:name="firebase_messaging_auto_init_enabled"
195-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
196            android:value="true" />
196-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
197        <meta-data
197-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
198            android:name="firebase_messaging_notification_delegation_enabled"
198-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
199            android:value="false" />
199-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
200        <meta-data
200-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
201            android:name="com.google.firebase.messaging.default_notification_channel_id"
201-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
202            android:value="" />
202-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
203        <meta-data
203-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
204            android:name="com.google.firebase.messaging.default_notification_color"
204-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
205            android:resource="@color/white" />
205-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
206        <meta-data
206-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
207            android:name="app_data_collection_default_enabled"
207-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
208            android:value="true" />
208-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
209
210        <service
210-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
211            android:name="com.google.firebase.components.ComponentDiscoveryService"
211-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
212            android:directBootAware="true"
212-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
213            android:exported="false" >
213-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
214            <meta-data
214-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
215                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
215-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
217            <meta-data
217-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
218                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
218-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
220            <meta-data
220-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
221                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
221-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
223            <meta-data
223-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:22:13-24:85
224                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayKtxRegistrar"
224-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:23:17-147
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:24:17-82
226            <meta-data
226-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:25:13-27:85
227                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayRegistrar"
227-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:26:17-144
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:27:17-82
229            <meta-data
229-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:14:13-16:85
230                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingKtxRegistrar"
230-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:15:17-132
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:16:17-82
232            <meta-data
232-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:17:13-19:85
233                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingRegistrar"
233-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:18:17-129
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\359ee9eb1dd0401d564f12308a996d54\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:19:17-82
235            <meta-data
235-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
236                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
236-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
238            <meta-data
238-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
239                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
239-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1aa20671a0d4570e23b9e78cd0c7b2\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
241            <meta-data
241-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
242                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
242-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b803b294e7dc28bce1192570be21d5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
244            <meta-data
244-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
245                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
245-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
247            <meta-data
247-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
248                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
248-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a2b20a7a1ab3ef88b6595471b9dac36\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
250            <meta-data
250-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
251                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
251-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2207c688751484b9dfcf9c3eb84e0e40\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
253        </service>
254
255        <provider
255-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
256            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
256-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
257            android:authorities="com.camelcase.rnstarbank.reactnativefirebaseappinitprovider"
257-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
258            android:exported="false"
258-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
259            android:initOrder="99" />
259-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
260        <provider
260-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
261            android:name="expo.modules.filesystem.FileSystemFileProvider"
261-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
262            android:authorities="com.camelcase.rnstarbank.FileSystemFileProvider"
262-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
263            android:exported="false"
263-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
264            android:grantUriPermissions="true" >
264-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
265            <meta-data
265-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
266                android:name="android.support.FILE_PROVIDER_PATHS"
266-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
267                android:resource="@xml/file_system_provider_paths" />
267-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
268        </provider>
269
270        <service
270-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
271            android:name="com.google.android.gms.metadata.ModuleDependencies"
271-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
272            android:enabled="false"
272-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
273            android:exported="false" >
273-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
274            <intent-filter>
274-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
275                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
275-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
275-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
276            </intent-filter>
277
278            <meta-data
278-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
279                android:name="photopicker_activity:0:required"
279-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
280                android:value="" />
280-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
281        </service>
282
283        <activity
283-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
284            android:name="com.canhub.cropper.CropImageActivity"
284-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
285            android:exported="true"
285-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
286            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
286-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
287        <provider
287-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
288            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
288-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
289            android:authorities="com.camelcase.rnstarbank.ImagePickerFileProvider"
289-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
290            android:exported="false"
290-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
291            android:grantUriPermissions="true" >
291-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
292            <meta-data
292-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
293                android:name="android.support.FILE_PROVIDER_PATHS"
293-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
294                android:resource="@xml/image_picker_provider_paths" />
294-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
295        </provider>
296
297        <service
297-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
298            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
298-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
299            android:exported="false" >
299-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
300            <intent-filter android:priority="-1" >
300-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
301                <action android:name="com.google.firebase.MESSAGING_EVENT" />
301-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
301-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
302            </intent-filter>
303        </service>
304
305        <receiver
305-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
306            android:name="expo.modules.notifications.service.NotificationsService"
306-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
307            android:enabled="true"
307-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
308            android:exported="false" >
308-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
309            <intent-filter android:priority="-1" >
309-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
309-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
310                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
310-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
310-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
311                <action android:name="android.intent.action.BOOT_COMPLETED" />
311-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
311-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
312                <action android:name="android.intent.action.REBOOT" />
312-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
312-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
313                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
313-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
313-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
314                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
314-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
314-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
315                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
315-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
315-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
316            </intent-filter>
317        </receiver>
318
319        <activity
319-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
320            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
320-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
321            android:excludeFromRecents="true"
321-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
322            android:exported="false"
322-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
323            android:launchMode="standard"
323-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
324            android:noHistory="true"
324-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
325            android:taskAffinity=""
325-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
326            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
326-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
327
328        <meta-data
328-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
329            android:name="org.unimodules.core.AppLoader#react-native-headless"
329-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
330            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
330-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
331        <meta-data
331-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
332            android:name="com.facebook.soloader.enabled"
332-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
333            android:value="true" />
333-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
334
335        <activity
335-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:19:9-21:40
336            android:name="com.facebook.react.devsupport.DevSettingsActivity"
336-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:20:13-77
337            android:exported="false" />
337-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\AndroidManifest.xml:21:13-37
338        <activity
338-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
339            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
339-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
340            android:excludeFromRecents="true"
340-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
341            android:exported="false"
341-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
342            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
342-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
343        <!--
344            Service handling Google Sign-In user revocation. For apps that do not integrate with
345            Google Sign-In, this service will never be started.
346        -->
347        <service
347-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
348            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
348-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
349            android:exported="true"
349-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
350            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
350-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
351            android:visibleToInstantApps="true" />
351-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
352
353        <meta-data
353-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
354            android:name="com.google.android.gms.version"
354-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
355            android:value="@integer/google_play_services_version" />
355-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
356
357        <receiver
357-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
358            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
358-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
359            android:exported="true"
359-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
360            android:permission="com.google.android.c2dm.permission.SEND" >
360-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
361            <intent-filter>
361-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
362                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
362-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
362-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
363            </intent-filter>
364
365            <meta-data
365-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
366                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
366-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
367                android:value="true" />
367-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
368        </receiver>
369        <!--
370             FirebaseMessagingService performs security checks at runtime,
371             but set to not exported to explicitly avoid allowing another app to call it.
372        -->
373        <service
373-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
374            android:name="com.google.firebase.messaging.FirebaseMessagingService"
374-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
375            android:directBootAware="true"
375-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
376            android:exported="false" >
376-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
377            <intent-filter android:priority="-500" >
377-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
378                <action android:name="com.google.firebase.MESSAGING_EVENT" />
378-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
378-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
379            </intent-filter>
380        </service>
381        <service
381-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
382            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
382-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
383            android:directBootAware="true"
383-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
384            android:exported="false" >
384-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
385            <meta-data
385-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
386                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
386-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
387                android:value="com.google.firebase.components.ComponentRegistrar" />
387-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2879a26fd54b24f3e2b55868cba50f28\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
388            <meta-data
388-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
389                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
389-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
390                android:value="com.google.firebase.components.ComponentRegistrar" />
390-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30603a13d06906d3d39fb90eb9a11456\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
391            <meta-data
391-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
392                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
392-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
393                android:value="com.google.firebase.components.ComponentRegistrar" />
393-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
394        </service>
395
396        <provider
396-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
397            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
397-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
398            android:authorities="com.camelcase.rnstarbank.mlkitinitprovider"
398-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
399            android:exported="false"
399-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
400            android:initOrder="99" />
400-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5d94f56a53476b0a5318133a1f19c0\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
401
402        <activity
402-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
403            android:name="com.google.android.gms.common.api.GoogleApiActivity"
403-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
404            android:exported="false"
404-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
405            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
405-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
406
407        <provider
407-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
408            android:name="com.google.firebase.provider.FirebaseInitProvider"
408-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
409            android:authorities="com.camelcase.rnstarbank.firebaseinitprovider"
409-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
410            android:directBootAware="true"
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
411            android:exported="false"
411-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
412            android:initOrder="100" />
412-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
413
414        <uses-library
414-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
415            android:name="androidx.camera.extensions.impl"
415-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
416            android:required="false" />
416-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
417
418        <service
418-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
419            android:name="androidx.camera.core.impl.MetadataHolderService"
419-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
420            android:enabled="false"
420-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
421            android:exported="false" >
421-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
422            <meta-data
422-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
423                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
423-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
424                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
424-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
425        </service>
426
427        <provider
427-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
428            android:name="com.canhub.cropper.CropFileProvider"
428-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
429            android:authorities="com.camelcase.rnstarbank.cropper.fileprovider"
429-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
430            android:exported="false"
430-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
431            android:grantUriPermissions="true" >
431-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
432            <meta-data
432-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
433                android:name="android.support.FILE_PROVIDER_PATHS"
433-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
434                android:resource="@xml/library_file_paths" />
434-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
435        </provider>
436        <provider
436-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
437            android:name="androidx.startup.InitializationProvider"
437-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
438            android:authorities="com.camelcase.rnstarbank.androidx-startup"
438-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
439            android:exported="false" >
439-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
440            <meta-data
440-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
441                android:name="androidx.work.WorkManagerInitializer"
441-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
442                android:value="androidx.startup" />
442-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
443            <meta-data
443-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
444                android:name="androidx.emoji2.text.EmojiCompatInitializer"
444-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
445                android:value="androidx.startup" />
445-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
446            <meta-data
446-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
447                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
447-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
448                android:value="androidx.startup" />
448-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
449            <meta-data
449-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
450                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
450-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
451                android:value="androidx.startup" />
451-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
452        </provider>
453
454        <service
454-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
455            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
455-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
456            android:directBootAware="false"
456-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
457            android:enabled="@bool/enable_system_alarm_service_default"
457-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
458            android:exported="false" />
458-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
459        <service
459-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
460            android:name="androidx.work.impl.background.systemjob.SystemJobService"
460-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
462            android:enabled="@bool/enable_system_job_service_default"
462-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
463            android:exported="true"
463-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
464            android:permission="android.permission.BIND_JOB_SERVICE" />
464-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
465        <service
465-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
466            android:name="androidx.work.impl.foreground.SystemForegroundService"
466-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
467            android:directBootAware="false"
467-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
468            android:enabled="@bool/enable_system_foreground_service_default"
468-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
469            android:exported="false" />
469-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
470
471        <receiver
471-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
472            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
472-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
473            android:directBootAware="false"
473-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
474            android:enabled="true"
474-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
475            android:exported="false" />
475-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
476        <receiver
476-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
477            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
477-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
478            android:directBootAware="false"
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
479            android:enabled="false"
479-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
480            android:exported="false" >
480-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
481            <intent-filter>
481-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
482                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
482-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
482-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
483                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
483-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
483-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
484            </intent-filter>
485        </receiver>
486        <receiver
486-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
487            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
487-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
488            android:directBootAware="false"
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
489            android:enabled="false"
489-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
490            android:exported="false" >
490-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
491            <intent-filter>
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
492                <action android:name="android.intent.action.BATTERY_OKAY" />
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
493                <action android:name="android.intent.action.BATTERY_LOW" />
493-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
493-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
494            </intent-filter>
495        </receiver>
496        <receiver
496-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
497            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
497-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
498            android:directBootAware="false"
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
499            android:enabled="false"
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
500            android:exported="false" >
500-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
501            <intent-filter>
501-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
502                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
503                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
503-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
503-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
504            </intent-filter>
505        </receiver>
506        <receiver
506-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
507            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
507-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
508            android:directBootAware="false"
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
509            android:enabled="false"
509-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
510            android:exported="false" >
510-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
511            <intent-filter>
511-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
512                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
513            </intent-filter>
514        </receiver>
515        <receiver
515-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
516            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
516-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
517            android:directBootAware="false"
517-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
518            android:enabled="false"
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
519            android:exported="false" >
519-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
520            <intent-filter>
520-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
521                <action android:name="android.intent.action.BOOT_COMPLETED" />
521-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
521-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
522                <action android:name="android.intent.action.TIME_SET" />
522-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
522-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
523                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
523-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
523-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
524            </intent-filter>
525        </receiver>
526        <receiver
526-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
527            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
527-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
528            android:directBootAware="false"
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
529            android:enabled="@bool/enable_system_alarm_service_default"
529-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
530            android:exported="false" >
530-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
531            <intent-filter>
531-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
532                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
533            </intent-filter>
534        </receiver>
535        <receiver
535-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
536            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
536-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
537            android:directBootAware="false"
537-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
538            android:enabled="true"
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
539            android:exported="true"
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
540            android:permission="android.permission.DUMP" >
540-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
541            <intent-filter>
541-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
542                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
542-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
542-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
543            </intent-filter>
544        </receiver>
545        <receiver
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
546            android:name="androidx.profileinstaller.ProfileInstallReceiver"
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
547            android:directBootAware="false"
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
548            android:enabled="true"
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
549            android:exported="true"
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
550            android:permission="android.permission.DUMP" >
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
551            <intent-filter>
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
552                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
553            </intent-filter>
554            <intent-filter>
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
555                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
555-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
555-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
556            </intent-filter>
557            <intent-filter>
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
558                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
558-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
558-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
559            </intent-filter>
560            <intent-filter>
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
561                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
561-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
561-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
562            </intent-filter>
563        </receiver>
564
565        <service
565-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
566            android:name="androidx.room.MultiInstanceInvalidationService"
566-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
567            android:directBootAware="true"
567-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
568            android:exported="false" />
568-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a6886cceffa9a2a1bddd1595fb25b68\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
569        <service
569-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
570            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
570-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
571            android:exported="false" >
571-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
572            <meta-data
572-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
573                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
573-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
574                android:value="cct" />
574-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20ff8de19a7a4767c6f8f140bbfed342\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
575        </service>
576        <service
576-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
577            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
577-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
578            android:exported="false"
578-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
579            android:permission="android.permission.BIND_JOB_SERVICE" >
579-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
580        </service>
581
582        <receiver
582-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
583            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
583-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
584            android:exported="false" />
584-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c52a514c7c6f1ed63c290b1560bb5713\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
585    </application>
586
587</manifest>
