<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#2e7d32</color>
    <color name="iconBackground">#FFFFFF</color>
    <color name="splashscreen_background">#2e7d32</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">SafariBank</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">automatic</string>
    <string name="gcm_defaultSenderId" translatable="false">************</string>
    <string name="google_api_key" translatable="false">AIzaSyDdYCY0UjflX0ToBJLkxWOOvSwLbOJ_B5o</string>
    <string name="google_app_id" translatable="false">1:************:android:0f96d3e5cb34eb0497ec0a</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDdYCY0UjflX0ToBJLkxWOOvSwLbOJ_B5o</string>
    <string name="google_storage_bucket" translatable="false">safaribank-d55a1.firebasestorage.app</string>
    <string name="project_id" translatable="false">safaribank-d55a1</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#2e7d32</item>
  </style>
    <style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>