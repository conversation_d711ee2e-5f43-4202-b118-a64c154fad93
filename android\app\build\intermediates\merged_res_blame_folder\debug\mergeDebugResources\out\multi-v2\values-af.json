{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-79:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,993,1074,1145,1220,1291,1362,1443,1513", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,988,1069,1140,1215,1286,1357,1438,1508,1628"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4129,4225,6696,6793,6894,7738,7814,11786,11876,12201,12279,12592,12900,13138,13279,13678,13759,13905", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "4220,4307,6788,6889,6975,7809,7900,11871,11957,12274,12355,12658,12970,13204,13345,13754,13824,14020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "279,387,483,589,674,777,895,972,1048,1139,1232,1327,1421,1520,1613,1708,1807,1902,1996,2077,2184,2289,2386,2494,2597,2699,2853,12511", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "382,478,584,669,772,890,967,1043,1134,1227,1322,1416,1515,1608,1703,1802,1897,1991,2072,2179,2284,2381,2489,2592,2694,2848,2946,12587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,352,436,505,575,652,730,813,892,964,1043,1122,1196,1280,1364,1443,1513,1583,1665,1740,1816,1888", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,206,278,347,431,500,570,647,725,808,887,959,1038,1117,1191,1275,1359,1438,1508,1578,1660,1735,1811,1883,1957"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2951,4312,7447,7585,7654,7968,8906,8976,9053,12039,12122,12360,12432,12663,12742,12816,12975,13059,13209,13350,13420,13603,13829,14025,14097", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "3019,4390,7514,7649,7733,8032,8971,9048,9126,12117,12196,12427,12506,12737,12811,12895,13054,13133,13274,13415,13497,13673,13900,14092,14166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5389", "endColumns": "142", "endOffsets": "5527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6591,7132,7234,7347", "endColumns": "104,101,112,99", "endOffsets": "6691,7229,7342,7442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14171,14258", "endColumns": "86,84", "endOffsets": "14253,14338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3102,3200,3302,3400,3498,3605,3714,13502", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3195,3297,3395,3493,3600,3709,3829,13598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4395,4502,4658,4784,4894,5044,5166,5287,5532,5698,5806,5963,6090,6229,6383,6449,6512", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "4497,4653,4779,4889,5039,5161,5282,5384,5693,5801,5958,6085,6224,6378,6444,6507,6586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,307,407,521,602,666,754,820,883,969,1030,1088,1154,1217,1272,1390,1447,1509,1564,1633,1752,1840,1923,2032,2115,2196,2283,2350,2416,2485,2561,2647,2721,2800,2873,2944,3031,3102,3191,3281,3353,3428,3515,3566,3633,3714,3798,3860,3924,3987,4091,4200,4296,4407", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "224,302,402,516,597,661,749,815,878,964,1025,1083,1149,1212,1267,1385,1442,1504,1559,1628,1747,1835,1918,2027,2110,2191,2278,2345,2411,2480,2556,2642,2716,2795,2868,2939,3026,3097,3186,3276,3348,3423,3510,3561,3628,3709,3793,3855,3919,3982,4086,4195,4291,4402,4479"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3024,3834,3934,4048,6980,7044,7519,7905,8037,8123,8184,8242,8308,8371,8426,8544,8601,8663,8718,8787,9131,9219,9302,9411,9494,9575,9662,9729,9795,9864,9940,10026,10100,10179,10252,10323,10410,10481,10570,10660,10732,10807,10894,10945,11012,11093,11177,11239,11303,11366,11470,11579,11675,11962", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "274,3097,3929,4043,4124,7039,7127,7580,7963,8118,8179,8237,8303,8366,8421,8539,8596,8658,8713,8782,8901,9214,9297,9406,9489,9570,9657,9724,9790,9859,9935,10021,10095,10174,10247,10318,10405,10476,10565,10655,10727,10802,10889,10940,11007,11088,11172,11234,11298,11361,11465,11574,11670,11781,12034"}}]}]}