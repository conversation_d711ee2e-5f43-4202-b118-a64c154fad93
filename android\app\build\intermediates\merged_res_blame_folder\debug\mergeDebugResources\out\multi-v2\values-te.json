{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-79:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,317,421,537,626,692,786,853,915,1008,1076,1139,1213,1278,1332,1453,1510,1572,1626,1705,1833,1921,2013,2128,2208,2290,2378,2445,2511,2586,2664,2754,2827,2903,2984,3053,3158,3235,3326,3419,3493,3570,3662,3717,3783,3867,3953,4016,4081,4145,4255,4367,4466,4585", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "226,312,416,532,621,687,781,848,910,1003,1071,1134,1208,1273,1327,1448,1505,1567,1621,1700,1828,1916,2008,2123,2203,2285,2373,2440,2506,2581,2659,2749,2822,2898,2979,3048,3153,3230,3321,3414,3488,3565,3657,3712,3778,3862,3948,4011,4076,4140,4250,4362,4461,4580,4663"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3107,3943,4047,4163,7109,7175,7665,8069,8199,8292,8360,8423,8497,8562,8616,8737,8794,8856,8910,8989,9335,9423,9515,9630,9710,9792,9880,9947,10013,10088,10166,10256,10329,10405,10486,10555,10660,10737,10828,10921,10995,11072,11164,11219,11285,11369,11455,11518,11583,11647,11757,11869,11968,12259", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "276,3188,4042,4158,4247,7170,7264,7727,8126,8287,8355,8418,8492,8557,8611,8732,8789,8851,8905,8984,9112,9418,9510,9625,9705,9787,9875,9942,10008,10083,10161,10251,10324,10400,10481,10550,10655,10732,10823,10916,10990,11067,11159,11214,11280,11364,11450,11513,11578,11642,11752,11864,11963,12082,12337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4518,4630,4784,4914,5029,5166,5291,5396,5634,5783,5895,6048,6180,6331,6494,6558,6628", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "4625,4779,4909,5024,5161,5286,5391,5491,5778,5890,6043,6175,6326,6489,6553,6623,6707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,348,430,498,565,640,716,801,883,954,1035,1115,1198,1284,1372,1450,1526,1601,1692,1764,1843,1912", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "122,201,273,343,425,493,560,635,711,796,878,949,1030,1110,1193,1279,1367,1445,1521,1596,1687,1759,1838,1907,1982"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3035,4439,7593,7732,7802,8131,9117,9184,9259,12342,12427,12683,12754,12995,13075,13158,13326,13414,13572,13727,13802,13994,14212,14408,14477", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "3102,4513,7660,7797,7879,8194,9179,9254,9330,12422,12504,12749,12830,13070,13153,13239,13409,13487,13643,13797,13888,14061,14286,14472,14547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3193,3295,3403,3505,3606,3712,3819,13893", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3290,3398,3500,3601,3707,3814,3938,13989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14552,14640", "endColumns": "87,94", "endOffsets": "14635,14730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,389,489,578,667,763,851,935,1019,1109,1186,1268,1348,1427,1504,1573", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "198,287,384,484,573,662,758,846,930,1014,1104,1181,1263,1343,1422,1499,1568,1685"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4252,4350,6823,6920,7020,7884,7973,12087,12175,12509,12593,12918,13244,13492,13648,14066,14143,14291", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "4345,4434,6915,7015,7104,7968,8064,12170,12254,12588,12678,12990,13321,13567,13722,14138,14207,14403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,272,383", "endColumns": "110,105,110,106", "endOffsets": "161,267,378,485"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6712,7269,7375,7486", "endColumns": "110,105,110,106", "endOffsets": "6818,7370,7481,7588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5496", "endColumns": "137", "endOffsets": "5629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "281,398,510,623,713,818,937,1015,1091,1182,1275,1370,1464,1564,1657,1752,1847,1938,2029,2118,2232,2336,2435,2550,2655,2770,2932,12835", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "393,505,618,708,813,932,1010,1086,1177,1270,1365,1459,1559,1652,1747,1842,1933,2024,2113,2227,2331,2430,2545,2650,2765,2927,3030,12913"}}]}]}