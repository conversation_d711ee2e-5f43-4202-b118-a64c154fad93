{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14691,14779", "endColumns": "87,94", "endOffsets": "14774,14869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5560", "endColumns": "137", "endOffsets": "5693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,303,398,531,620,686,783,863,925,1014,1079,1138,1211,1274,1328,1456,1513,1575,1629,1702,1845,1929,2017,2123,2211,2299,2384,2451,2517,2592,2668,2754,2831,2907,2984,3058,3149,3224,3315,3407,3481,3568,3659,3714,3780,3863,3949,4011,4075,4138,4255,4368,4479,4596", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "215,298,393,526,615,681,778,858,920,1009,1074,1133,1206,1269,1323,1451,1508,1570,1624,1697,1840,1924,2012,2118,2206,2294,2379,2446,2512,2587,2663,2749,2826,2902,2979,3053,3144,3219,3310,3402,3476,3563,3654,3709,3775,3858,3944,4006,4070,4133,4250,4363,4474,4591,4677"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3089,3913,4008,4141,7218,7284,7784,8194,8325,8414,8479,8538,8611,8674,8728,8856,8913,8975,9029,9102,9474,9558,9646,9752,9840,9928,10013,10080,10146,10221,10297,10383,10460,10536,10613,10687,10778,10853,10944,11036,11110,11197,11288,11343,11409,11492,11578,11640,11704,11767,11884,11997,12108,12400", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "265,3167,4003,4136,4225,7279,7376,7859,8251,8409,8474,8533,8606,8669,8723,8851,8908,8970,9024,9097,9240,9553,9641,9747,9835,9923,10008,10075,10141,10216,10292,10378,10455,10531,10608,10682,10773,10848,10939,11031,11105,11192,11283,11338,11404,11487,11573,11635,11699,11762,11879,11992,12103,12220,12481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4510,4619,4784,4919,5030,5197,5332,5451,5698,5867,5979,6154,6292,6449,6615,6685,6744", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "4614,4779,4914,5025,5192,5327,5446,5555,5862,5974,6149,6287,6444,6610,6680,6739,6812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,390,496,601,687,797,918,998,1075,1166,1259,1354,1448,1548,1641,1736,1844,1935,2026,2109,2223,2331,2431,2545,2652,2760,2920,12983", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "385,491,596,682,792,913,993,1070,1161,1254,1349,1443,1543,1636,1731,1839,1930,2021,2104,2218,2326,2426,2540,2647,2755,2915,3014,13062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,282,357,445,514,581,661,743,830,910,981,1068,1155,1229,1308,1390,1467,1544,1619,1703,1778,1860,1930", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "120,204,277,352,440,509,576,656,738,825,905,976,1063,1150,1224,1303,1385,1462,1539,1614,1698,1773,1855,1925,2010"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3019,4426,7711,7864,7939,8256,9245,9312,9392,12486,12573,12825,12896,13139,13226,13300,13456,13538,13693,13846,13921,14106,14333,14536,14606", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "3084,4505,7779,7934,8022,8320,9307,9387,9469,12568,12648,12891,12978,13221,13295,13374,13533,13610,13765,13916,14000,14176,14410,14601,14686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3172,3269,3379,3481,3582,3689,3794,14005", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3264,3374,3476,3577,3684,3789,3908,14101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4230,4333,6928,7031,7134,8027,8103,12225,12316,12653,12737,13067,13379,13615,13770,14181,14264,14415", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "4328,4421,7026,7129,7213,8098,8189,12311,12395,12732,12820,13134,13451,13688,13841,14259,14328,14531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6817,7381,7489,7601", "endColumns": "110,107,111,109", "endOffsets": "6923,7484,7596,7706"}}]}]}