{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5519", "endColumns": "131", "endOffsets": "5646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "67,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6694,7246,7346,7460", "endColumns": "104,99,113,101", "endOffsets": "6794,7341,7455,7557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,360,456,586,670,738,834,902,965,1073,1139,1195,1266,1326,1380,1506,1563,1625,1679,1754,1888,1973,2054,2161,2245,2331,2422,2489,2555,2629,2707,2795,2867,2944,3024,3098,3191,3264,3356,3452,3526,3602,3698,3750,3817,3904,3991,4053,4117,4180,4286,4387,4484,4588", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "270,355,451,581,665,733,829,897,960,1068,1134,1190,1261,1321,1375,1501,1558,1620,1674,1749,1883,1968,2049,2156,2240,2326,2417,2484,2550,2624,2702,2790,2862,2939,3019,3093,3186,3259,3351,3447,3521,3597,3693,3745,3812,3899,3986,4048,4112,4175,4281,4382,4479,4583,4663"}, "to": {"startLines": "2,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3110,3921,4017,4147,7082,7150,7636,8026,8156,8264,8330,8386,8457,8517,8571,8697,8754,8816,8870,8945,9314,9399,9480,9587,9671,9757,9848,9915,9981,10055,10133,10221,10293,10370,10450,10524,10617,10690,10782,10878,10952,11028,11124,11176,11243,11330,11417,11479,11543,11606,11712,11813,11910,12194", "endLines": "6,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "320,3190,4012,4142,4226,7145,7241,7699,8084,8259,8325,8381,8452,8512,8566,8692,8749,8811,8865,8940,9074,9394,9475,9582,9666,9752,9843,9910,9976,10050,10128,10216,10288,10365,10445,10519,10612,10685,10777,10873,10947,11023,11119,11171,11238,11325,11412,11474,11538,11601,11707,11808,11905,12009,12269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "162,163", "startColumns": "4,4", "startOffsets": "14486,14569", "endColumns": "82,84", "endOffsets": "14564,14649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1013,1099,1172,1249,1328,1405,1484,1554", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,1008,1094,1167,1244,1323,1400,1479,1549,1667"}, "to": {"startLines": "46,47,68,69,70,80,81,132,133,137,138,142,146,149,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4231,4336,6799,6893,6992,7854,7933,12014,12109,12447,12528,12857,13169,13407,13564,13981,14060,14209", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "4331,4418,6888,6987,7077,7928,8021,12104,12189,12523,12609,12925,13241,13481,13636,14055,14125,14322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "34,48,76,78,79,83,96,97,98,135,136,139,140,143,144,145,147,148,150,152,153,155,158,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,4423,7562,7704,7773,8089,9079,9150,9231,12274,12358,12614,12686,12930,13013,13089,13246,13328,13486,13641,13717,13908,14130,14327,14405", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3105,4502,7631,7768,7849,8151,9145,9226,9309,12353,12442,12681,12767,13008,13084,13164,13323,13402,13559,13712,13802,13976,14204,14400,14481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,430,525,632,718,822,941,1026,1108,1199,1292,1387,1481,1581,1674,1769,1864,1955,2046,2132,2236,2348,2449,2554,2668,2770,2939,12772", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "425,520,627,713,817,936,1021,1103,1194,1287,1382,1476,1576,1669,1764,1859,1950,2041,2127,2231,2343,2444,2549,2663,2765,2934,3031,12852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4507,4613,4770,4900,5010,5167,5297,5412,5651,5801,5908,6065,6193,6340,6483,6551,6613", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4608,4765,4895,5005,5162,5292,5407,5514,5796,5903,6060,6188,6335,6478,6546,6608,6689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "36,37,38,39,40,41,42,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3293,3400,3497,3596,3700,3804,13807", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3288,3395,3492,3591,3695,3799,3916,13903"}}]}]}