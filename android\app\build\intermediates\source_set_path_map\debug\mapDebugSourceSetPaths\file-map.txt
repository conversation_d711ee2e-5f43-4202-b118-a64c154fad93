com.camelcase.rnstarbank.app-ui-text-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004bf2b17555a1d78bc2e33d5e6648a9\transformed\ui-text-release\res
com.camelcase.rnstarbank.app-ui-release-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0245299d52204dfeed11ab2f0057dc07\transformed\ui-release\res
com.camelcase.rnstarbank.app-annotation-experimental-1.4.1-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b927f0a827a362ae4d5f43d0882c12\transformed\annotation-experimental-1.4.1\res
com.camelcase.rnstarbank.app-drawerlayout-1.1.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\078f9f02e495f0dd0d4e161296c58ceb\transformed\drawerlayout-1.1.1\res
com.camelcase.rnstarbank.app-foundation-release-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ed4efd95d8440ba0b6c7542219c081\transformed\foundation-release\res
com.camelcase.rnstarbank.app-appcompat-1.7.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f02e74a5d81ac387218e6b268b524af\transformed\appcompat-1.7.0\res
com.camelcase.rnstarbank.app-play-services-basement-18.4.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ae654536d6c9ca98ea7772e1c0edcc\transformed\play-services-basement-18.4.0\res
com.camelcase.rnstarbank.app-lifecycle-service-2.8.3-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163bb7dfa58f2e00c622862ee5c8da95\transformed\lifecycle-service-2.8.3\res
com.camelcase.rnstarbank.app-lifecycle-runtime-ktx-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b51e513eaac6ddc479b7ab44cbe758\transformed\lifecycle-runtime-ktx-release\res
com.camelcase.rnstarbank.app-lifecycle-runtime-release-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aff1c1c001931892e6322b21062b5f\transformed\lifecycle-runtime-release\res
com.camelcase.rnstarbank.app-savedstate-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f773416775a47656b455505e7c2d836\transformed\savedstate-1.2.1\res
com.camelcase.rnstarbank.app-emoji2-views-helper-1.3.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30007fc325d59e5b0197043eba024cce\transformed\emoji2-views-helper-1.3.0\res
com.camelcase.rnstarbank.app-ui-unit-release-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31aae105eb04a406e2ec638f81d2b6bb\transformed\ui-unit-release\res
com.camelcase.rnstarbank.app-lifecycle-livedata-core-2.8.3-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37213c2832ac8fb03f8a9407c7109e91\transformed\lifecycle-livedata-core-2.8.3\res
com.camelcase.rnstarbank.app-glide-4.16.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37b335fa6456d069fae66478cd3f5d86\transformed\glide-4.16.0\res
com.camelcase.rnstarbank.app-BlurView-version-2.0.6-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39aaf4560a09f74bd78eba6ff9a489dd\transformed\BlurView-version-2.0.6\res
com.camelcase.rnstarbank.app-fragment-1.6.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39ab0d272159339cff56d09d2456bfd2\transformed\fragment-1.6.1\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-savedstate-2.8.3-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ada69cf34ac3768f494c8eaa4a4a91a\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.camelcase.rnstarbank.app-ui-graphics-release-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41326908fbc54526a8c2668ccc81005d\transformed\ui-graphics-release\res
com.camelcase.rnstarbank.app-camera-extensions-1.4.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\496e95a24f1d9dedfc63327d2ab86c7b\transformed\camera-extensions-1.4.1\res
com.camelcase.rnstarbank.app-core-runtime-2.2.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea564785c7682172e5c76fbb4c29e3c\transformed\core-runtime-2.2.0\res
com.camelcase.rnstarbank.app-tracing-ktx-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f9f12d82a908e44f849c25bba51e2dc\transformed\tracing-ktx-1.2.0\res
com.camelcase.rnstarbank.app-viewpager2-1.1.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fbd21e0f03e612ad3ec9e9d852e13a9\transformed\viewpager2-1.1.0\res
com.camelcase.rnstarbank.app-startup-runtime-1.1.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56528e95ba5582c734f52c0d27c03241\transformed\startup-runtime-1.1.1\res
com.camelcase.rnstarbank.app-core-splashscreen-1.2.0-alpha02-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57b5082326aaded3911b6e6ec44be48d\transformed\core-splashscreen-1.2.0-alpha02\res
com.camelcase.rnstarbank.app-foundation-layout-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1dfd1b5a83d588f5e603577b9178d0\transformed\foundation-layout-release\res
com.camelcase.rnstarbank.app-runtime-release-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e51d0b3859d8154ebe72962e057ff1\transformed\runtime-release\res
com.camelcase.rnstarbank.app-firebase-inappmessaging-display-21.0.2-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6303685551a09f0ba4a2de4444fd7cd1\transformed\firebase-inappmessaging-display-21.0.2\res
com.camelcase.rnstarbank.app-customview-poolingcontainer-1.0.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e1fc5fcaa3ed5f5449494b4af1d023\transformed\customview-poolingcontainer-1.0.0\res
com.camelcase.rnstarbank.app-recyclerview-1.3.1-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64916dec3207c110b65653508cdd652c\transformed\recyclerview-1.3.1\res
com.camelcase.rnstarbank.app-lifecycle-livedata-core-ktx-2.8.3-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44ebbf2f2c8625d954228a1184185d\transformed\lifecycle-livedata-core-ktx-2.8.3\res
com.camelcase.rnstarbank.app-savedstate-ktx-1.2.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e397bb62bef4ae7fa0227e458b86cc6\transformed\savedstate-ktx-1.2.1\res
com.camelcase.rnstarbank.app-react-android-0.76.6-debug-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\735ffdc777bf493d1b59bb9260f05d0b\transformed\react-android-0.76.6-debug\res
com.camelcase.rnstarbank.app-browser-1.6.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78058efdb2a613945fc64792040e9310\transformed\browser-1.6.0\res
com.camelcase.rnstarbank.app-Android-Image-Cropper-4.3.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d8ce858a08fb06de78ffa9e22ca9ac\transformed\Android-Image-Cropper-4.3.1\res
com.camelcase.rnstarbank.app-drawee-3.2.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795db646d5257b1dffa43100532574a6\transformed\drawee-3.2.0\res
com.camelcase.rnstarbank.app-play-services-auth-21.3.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79d77f70ab6d77d986ef7a12134bccba\transformed\play-services-auth-21.3.0\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-release-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e111718ef3632e36e79757ab187886b\transformed\lifecycle-viewmodel-release\res
com.camelcase.rnstarbank.app-activity-ktx-1.7.2-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8038d679c6201ccc8bf88cea2ee85a9a\transformed\activity-ktx-1.7.2\res
com.camelcase.rnstarbank.app-android-maps-utils-3.8.2-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a4775868c76451db4b208a8dc33864\transformed\android-maps-utils-3.8.2\res
com.camelcase.rnstarbank.app-camera-view-1.4.1-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aa52d47348985c73b8cf35e1e65e62\transformed\camera-view-1.4.1\res
com.camelcase.rnstarbank.app-lifecycle-process-2.8.3-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1a2636439c5c64ef4337b94b804ac\transformed\lifecycle-process-2.8.3\res
com.camelcase.rnstarbank.app-animation-core-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87fa0d78838279af547bb791a0359734\transformed\animation-core-release\res
com.camelcase.rnstarbank.app-runtime-saveable-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\880c43895efa6facf988b03ab593d472\transformed\runtime-saveable-release\res
com.camelcase.rnstarbank.app-media-1.0.0-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8921afe01ea231b105108b56412c9411\transformed\media-1.0.0\res
com.camelcase.rnstarbank.app-swiperefreshlayout-1.1.0-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f899b0c191eabd106cb0793f29ac337\transformed\swiperefreshlayout-1.1.0\res
com.camelcase.rnstarbank.app-material-1.6.1-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fba611268d5cb39dc8b46f5d8eaefc2\transformed\material-1.6.1\res
com.camelcase.rnstarbank.app-play-services-maps-18.2.0-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96c271313809ed095a43a1602a44b18d\transformed\play-services-maps-18.2.0\res
com.camelcase.rnstarbank.app-animation-release-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99cc69e026378dbd6f90620a53792b58\transformed\animation-release\res
com.camelcase.rnstarbank.app-firebase-messaging-24.1.2-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db8b39eb2c8a80a6af70059f57005f1\transformed\firebase-messaging-24.1.2\res
com.camelcase.rnstarbank.app-coordinatorlayout-1.2.0-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dd958c62274c71174c6ad8e15200343\transformed\coordinatorlayout-1.2.0\res
com.camelcase.rnstarbank.app-lifecycle-runtime-compose-release-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ffb77c3facd0be7cf360be32700013e\transformed\lifecycle-runtime-compose-release\res
com.camelcase.rnstarbank.app-firebase-common-21.0.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afedc52b7d37bb068dfbf08571b8e1be\transformed\firebase-common-21.0.0\res
com.camelcase.rnstarbank.app-appcompat-resources-1.7.0-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b105516173dd17fd78ec8185e8cc5101\transformed\appcompat-resources-1.7.0\res
com.camelcase.rnstarbank.app-fragment-ktx-1.6.1-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b22009fa58ae9cfbdc13cf4633cdd1c8\transformed\fragment-ktx-1.6.1\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-ktx-2.8.3-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2b839604b972af639853fc4fcf6ad4e\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.camelcase.rnstarbank.app-profileinstaller-1.3.1-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b85d4043d04481e0ab99d6fe7b38d5df\transformed\profileinstaller-1.3.1\res
com.camelcase.rnstarbank.app-transition-1.2.0-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8dcdc5c56667393d0b3e956be36660f\transformed\transition-1.2.0\res
com.camelcase.rnstarbank.app-cardview-1.0.0-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2cefc9402a998496405605999dac44\transformed\cardview-1.0.0\res
com.camelcase.rnstarbank.app-camera-core-1.4.1-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc5ca54576cb79e4c839264cf58132d9\transformed\camera-core-1.4.1\res
com.camelcase.rnstarbank.app-emoji2-1.3.0-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3563f05e49a1c7120460d84d54fcd00\transformed\emoji2-1.3.0\res
com.camelcase.rnstarbank.app-camera-video-1.4.1-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6cd8ec49b429079797bd87a9cbd6ff3\transformed\camera-video-1.4.1\res
com.camelcase.rnstarbank.app-tracing-1.2.0-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c77ba46fd4f7461df3ea066903c0cf73\transformed\tracing-1.2.0\res
com.camelcase.rnstarbank.app-activity-1.7.2-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c962283642e48ac832ef3cf069ec7d35\transformed\activity-1.7.2\res
com.camelcase.rnstarbank.app-play-services-base-18.5.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cecc86839484e6f0730f8d8c4e50f16e\transformed\play-services-base-18.5.0\res
com.camelcase.rnstarbank.app-work-runtime-2.7.1-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d27f2ad1790d9e629f16995caba6fc7a\transformed\work-runtime-2.7.1\res
com.camelcase.rnstarbank.app-camera-lifecycle-1.4.1-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe3ee9224a8196eee6ee54c26b36d74\transformed\camera-lifecycle-1.4.1\res
com.camelcase.rnstarbank.app-lifecycle-livedata-2.8.3-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca31741dd4b7ed918dbb9b294b453c3\transformed\lifecycle-livedata-2.8.3\res
com.camelcase.rnstarbank.app-constraintlayout-2.1.4-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de67b124d596d095d02c3f2d0f5df8f5\transformed\constraintlayout-2.1.4\res
com.camelcase.rnstarbank.app-ui-geometry-release-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2927c22491b7bae5c3e8b02cbb6dcaf\transformed\ui-geometry-release\res
com.camelcase.rnstarbank.app-ui-util-release-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58ecd19d0aa0f88f0cfc56e03515ed8\transformed\ui-util-release\res
com.camelcase.rnstarbank.app-camera-mlkit-vision-1.4.1-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba63eea34968ac021cedbd1b85bd502\transformed\camera-mlkit-vision-1.4.1\res
com.camelcase.rnstarbank.app-graphics-path-1.0.1-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed26e31f373a353d6df51f524243fa77\transformed\graphics-path-1.0.1\res
com.camelcase.rnstarbank.app-autofill-1.1.0-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe005d8bf59ba00e237aa64d6c04b6d\transformed\autofill-1.1.0\res
com.camelcase.rnstarbank.app-core-ktx-1.13.1-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f701b1c825c6e4f40e596908bba825e7\transformed\core-ktx-1.13.1\res
com.camelcase.rnstarbank.app-core-1.13.1-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9889622b896471c426b236842b6dae1\transformed\core-1.13.1\res
com.camelcase.rnstarbank.app-camera-camera2-1.4.1-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd6a4f1ce54e379fe20fef484a6c3b40\transformed\camera-camera2-1.4.1\res
com.camelcase.rnstarbank.app-pngs-77 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\pngs\debug
com.camelcase.rnstarbank.app-res-78 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\processDebugGoogleServices
com.camelcase.rnstarbank.app-resValues-79 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\resValues\debug
com.camelcase.rnstarbank.app-packageDebugResources-80 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.camelcase.rnstarbank.app-packageDebugResources-81 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.camelcase.rnstarbank.app-debug-82 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.camelcase.rnstarbank.app-debug-83 C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\res
com.camelcase.rnstarbank.app-main-84 C:\Users\<USER>\Desktop\safaribank\android\app\src\main\res
com.camelcase.rnstarbank.app-debug-85 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-86 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-87 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-88 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-89 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-90 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-91 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-92 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-93 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-94 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-95 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-96 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-97 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-98 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-99 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-100 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-101 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-102 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-103 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-104 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-105 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-106 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-107 C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-108 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-109 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-110 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-111 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-112 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-113 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-114 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-115 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-116 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
