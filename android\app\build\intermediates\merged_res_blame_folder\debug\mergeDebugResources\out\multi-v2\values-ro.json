{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-79:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5532", "endColumns": "143", "endOffsets": "5671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "36,37,38,39,40,41,42,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3218,3316,3418,3518,3617,3719,3828,13833", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3311,3413,3513,3612,3714,3823,3940,13929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "67,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6773,7328,7430,7543", "endColumns": "106,101,112,102", "endOffsets": "6875,7425,7538,7641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1162,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1231,1310,1385,1462,1529,1644"}, "to": {"startLines": "46,47,68,69,70,80,81,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4251,4348,6880,6977,7079,7945,8023,12051,12142,12478,12566,12891,13211,13442,13599,14007,14084,14227", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "4343,4427,6972,7074,7162,8018,8105,12137,12219,12561,12651,12959,13280,13516,13669,14079,14146,14337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,274,345,429,497,573,657,742,832,901,985,1075,1150,1232,1311,1389,1467,1541,1626,1699,1775", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,199,269,340,424,492,568,652,737,827,896,980,1070,1145,1227,1306,1384,1462,1536,1621,1694,1770,1855"}, "to": {"startLines": "34,48,76,78,79,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3061,4432,7646,7790,7861,9082,9150,9226,12303,12388,12656,12725,12964,13054,13129,13285,13364,13521,13674,13748,13934,14151,14342", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3126,4506,7711,7856,7940,9145,9221,9305,12383,12473,12720,12804,13049,13124,13206,13359,13437,13594,13743,13828,14002,14222,14422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,449,555,668,752,857,976,1061,1141,1232,1325,1420,1514,1614,1707,1802,1896,1987,2079,2160,2270,2378,2476,2588,2694,2798,2960,12809", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "444,550,663,747,852,971,1056,1136,1227,1320,1415,1509,1609,1702,1797,1891,1982,2074,2155,2265,2373,2471,2583,2689,2793,2955,3056,12886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4511,4619,4779,4909,5019,5170,5300,5423,5676,5838,5949,6108,6241,6387,6553,6622,6690", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "4614,4774,4904,5014,5165,5295,5418,5527,5833,5944,6103,6236,6382,6548,6617,6685,6768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "160,161", "startColumns": "4,4", "startOffsets": "14427,14522", "endColumns": "94,99", "endOffsets": "14517,14617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "2,35,43,44,45,71,72,77,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3131,3945,4046,4167,7167,7233,7716,8110,8170,8254,8320,8378,8451,8514,8570,8689,8746,8807,8863,8937,9310,9396,9480,9583,9665,9748,9838,9905,9971,10044,10122,10210,10281,10358,10432,10504,10595,10669,10764,10862,10936,11016,11117,11170,11236,11325,11415,11477,11541,11604,11716,11829,11939,12224", "endLines": "6,35,43,44,45,71,72,77,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "321,3213,4041,4162,4246,7228,7323,7785,8165,8249,8315,8373,8446,8509,8565,8684,8741,8802,8858,8932,9077,9391,9475,9578,9660,9743,9833,9900,9966,10039,10117,10205,10276,10353,10427,10499,10590,10664,10759,10857,10931,11011,11112,11165,11231,11320,11410,11472,11536,11599,11711,11824,11934,12046,12298"}}]}]}