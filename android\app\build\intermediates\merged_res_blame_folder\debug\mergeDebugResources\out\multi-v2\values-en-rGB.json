{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "34,35,36,37,38,39,40,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3011,3107,3209,3308,3407,3511,3614,12398", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3102,3204,3303,3402,3506,3609,3725,12494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "46,75,91,92,129,130,133,136,143,145", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4196,7336,8533,8607,11544,11626,11866,12100,12642,12837", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4267,7414,8602,8678,11621,11701,11939,12175,12711,12906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,402,517,596,661,751,818,877,967,1031,1094,1163,1227,1281,1393,1451,1513,1567,1639,1761,1848,1929,2039,2116,2197,2288,2355,2421,2491,2568,2655,2726,2803,2872,2941,3032,3104,3193,3282,3356,3428,3514,3564,3630,3710,3794,3856,3920,3983,4083,4180,4272,4371", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "222,299,397,512,591,656,746,813,872,962,1026,1089,1158,1222,1276,1388,1446,1508,1562,1634,1756,1843,1924,2034,2111,2192,2283,2350,2416,2486,2563,2650,2721,2798,2867,2936,3027,3099,3188,3277,3351,3423,3509,3559,3625,3705,3789,3851,3915,3978,4078,4175,4267,4366,4444"}, "to": {"startLines": "2,33,41,42,43,69,70,74,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2934,3730,3828,3943,6809,6874,7269,7590,7649,7739,7803,7866,7935,7999,8053,8165,8223,8285,8339,8411,8683,8770,8851,8961,9038,9119,9210,9277,9343,9413,9490,9577,9648,9725,9794,9863,9954,10026,10115,10204,10278,10350,10436,10486,10552,10632,10716,10778,10842,10905,11005,11102,11194,11466", "endLines": "5,33,41,42,43,69,70,74,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "272,3006,3823,3938,4017,6869,6959,7331,7644,7734,7798,7861,7930,7994,8048,8160,8218,8280,8334,8406,8528,8765,8846,8956,9033,9114,9205,9272,9338,9408,9485,9572,9643,9720,9789,9858,9949,10021,10110,10199,10273,10345,10431,10481,10547,10627,10711,10773,10837,10900,11000,11097,11189,11288,11539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,381,481,589,673,773,888,966,1041,1132,1225,1320,1414,1514,1607,1702,1796,1887,1978,2060,2163,2266,2365,2470,2574,2678,2834,11944", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "376,476,584,668,768,883,961,1036,1127,1220,1315,1409,1509,1602,1697,1791,1882,1973,2055,2158,2261,2360,2465,2569,2673,2829,2929,12022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "65,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6431,6964,7061,7170", "endColumns": "97,96,108,98", "endOffsets": "6524,7056,7165,7264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "146,147", "startColumns": "4,4", "startOffsets": "12911,12997", "endColumns": "85,84", "endOffsets": "12992,13077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "44,45,66,67,68,76,77,126,127,131,132,135,137,138,139,141,142,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4022,4114,6529,6623,6722,7419,7501,11293,11382,11706,11784,12027,12180,12256,12328,12499,12576,12716", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "4109,4191,6618,6717,6804,7496,7585,11377,11461,11779,11861,12095,12251,12323,12393,12571,12637,12832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4272,4377,4529,4654,4761,4912,5035,5151,5392,5551,5656,5808,5933,6079,6227,6290,6352", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "4372,4524,4649,4756,4907,5030,5146,5249,5546,5651,5803,5928,6074,6222,6285,6347,6426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5254", "endColumns": "137", "endOffsets": "5387"}}]}]}