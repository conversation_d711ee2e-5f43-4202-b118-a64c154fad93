{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-79:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78d8ce858a08fb06de78ffa9e22ca9ac\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,211,268,312,378,442,515,568", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "159,206,263,307,373,437,510,563,627"}, "to": {"startLines": "65,66,67,76,77,78,79,80,133", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6509,6618,6665,7473,7517,7583,7647,7720,11790", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "6613,6660,6717,7512,7578,7642,7715,7768,11849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fba611268d5cb39dc8b46f5d8eaefc2\\transformed\\material-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,303,402,521,603,667,759,827,887,974,1036,1100,1168,1233,1287,1396,1454,1516,1570,1645,1765,1847,1927,2031,2109,2189,2277,2344,2410,2478,2552,2642,2713,2791,2861,2931,3020,3098,3186,3276,3348,3420,3504,3555,3621,3702,3785,3847,3911,3974,4074,4172,4265,4363", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "211,298,397,516,598,662,754,822,882,969,1031,1095,1163,1228,1282,1391,1449,1511,1565,1640,1760,1842,1922,2026,2104,2184,2272,2339,2405,2473,2547,2637,2708,2786,2856,2926,3015,3093,3181,3271,3343,3415,3499,3550,3616,3697,3780,3842,3906,3969,4069,4167,4260,4358,4436"}, "to": {"startLines": "2,33,41,42,43,71,72,81,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2898,3711,3810,3929,7007,7071,7773,8005,8065,8152,8214,8278,8346,8411,8465,8574,8632,8694,8748,8823,8943,9025,9105,9209,9287,9367,9455,9522,9588,9656,9730,9820,9891,9969,10039,10109,10198,10276,10364,10454,10526,10598,10682,10733,10799,10880,10963,11025,11089,11152,11252,11350,11443,11712", "endLines": "5,33,41,42,43,71,72,81,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "261,2980,3805,3924,4006,7066,7158,7836,8060,8147,8209,8273,8341,8406,8460,8569,8627,8689,8743,8818,8938,9020,9100,9204,9282,9362,9450,9517,9583,9651,9725,9815,9886,9964,10034,10104,10193,10271,10359,10449,10521,10593,10677,10728,10794,10875,10958,11020,11084,11147,11247,11345,11438,11536,11785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09ed4efd95d8440ba0b6c7542219c081\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "145,146", "startColumns": "4,4", "startOffsets": "12746,12836", "endColumns": "89,87", "endOffsets": "12831,12919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cecc86839484e6f0730f8d8c4e50f16e\\transformed\\play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4185,4291,4450,4576,4685,4841,4971,5091,5324,5478,5585,5746,5874,6016,6192,6259,6321", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4286,4445,4571,4680,4836,4966,5086,5189,5473,5580,5741,5869,6011,6187,6254,6316,6394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\14ae654536d6c9ca98ea7772e1c0edcc\\transformed\\play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5194", "endColumns": "129", "endOffsets": "5319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78058efdb2a613945fc64792040e9310\\transformed\\browser-1.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "64,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6399,7163,7264,7376", "endColumns": "109,100,111,96", "endOffsets": "6504,7259,7371,7468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f02e74a5d81ac387218e6b268b524af\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "266,369,464,578,664,764,877,954,1029,1120,1213,1307,1401,1501,1594,1689,1787,1878,1969,2047,2150,2248,2344,2448,2547,2648,2801,12016", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "364,459,573,659,759,872,949,1024,1115,1208,1302,1396,1496,1589,1684,1782,1873,1964,2042,2145,2243,2339,2443,2542,2643,2796,2893,12091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0245299d52204dfeed11ab2f0057dc07\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,979,1061,1131,1205,1276,1346,1423,1490", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,974,1056,1126,1200,1271,1341,1418,1485,1605"}, "to": {"startLines": "44,45,68,69,70,82,83,130,131,134,135,137,138,139,140,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4011,4104,6722,6819,6919,7841,7917,11541,11630,11854,11934,12096,12166,12240,12311,12482,12559,12626", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "4099,4180,6814,6914,7002,7912,8000,11625,11707,11929,12011,12161,12235,12306,12376,12554,12621,12741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9889622b896471c426b236842b6dae1\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "34,35,36,37,38,39,40,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2985,3079,3181,3278,3377,3485,3591,12381", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3074,3176,3273,3372,3480,3586,3706,12477"}}]}]}